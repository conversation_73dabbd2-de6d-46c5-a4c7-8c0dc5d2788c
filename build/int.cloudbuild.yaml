# Copyright 2019 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

timeout: 3600s
steps:
- id: swap-module-refs
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['module-swapper']
- id: prepare
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && prepare_environment']
  env:
  - 'TF_VAR_org_id=$_ORG_ID'
  - 'TF_VAR_folder_id=$_FOLDER_ID'
  - 'TF_VAR_billing_account=$_BILLING_ACCOUNT'
  - 'TF_VAR_billing_iam_test_account=$_BILLING_IAM_TEST_ACCOUNT'

- id: create all
  waitFor:
    - prepare
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do create']
- id: converge member-iam-local
  waitFor:
    - create all
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge member-iam-local']
- id: verify member-iam-local
  waitFor:
    - converge member-iam-local
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify member-iam-local']
- id: destroy member-iam-local
  waitFor:
    - verify member-iam-local
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do destroy member-iam-local']

# ----- SUITE billing-iam-local

- id: converge billing-iam-local
  waitFor:
    - create all
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge billing-iam-local']
- id: verify billing-iam-local
  waitFor:
    - converge billing-iam-local
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify billing-iam-local']
- id: destroy billing-iam-local
  waitFor:
    - verify billing-iam-local
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do destroy billing-iam-local']

# ----- SUITE custom-role-local

- id: converge custom-role-local
  waitFor:
    - create all
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge custom-role-local']
- id: verify custom-role-local
  waitFor:
    - converge custom-role-local
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify custom-role-local']
- id: destroy custom-role-local
  waitFor:
    - verify custom-role-local
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do destroy custom-role-local']

# ----- SUITE additive-local

# verify additive-local with 2 roles
- id: converge additive-local 2 roles
  waitFor:
  - create all
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge additive-local']
  env:
  - 'TF_VAR_roles=2'
- id: verify additive-local 2 roles
  waitFor:
  - converge additive-local 2 roles
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify additive-local']
  env:
  - 'TF_VAR_roles=2'

# scale additive-local to 1 role and reverify
- id: converge additive-local 1 role
  waitFor:
  - verify additive-local 2 roles
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge additive-local']
  env:
  - 'TF_VAR_roles=1'
- id: verify additive-local 1 role
  waitFor:
  - converge additive-local 1 role
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify additive-local']
  env:
  - 'TF_VAR_roles=1'

# scale additive-local back to 2 roles and reverify
- id: converge additive-local 2 roles again
  waitFor:
  - verify additive-local 1 role
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge additive-local']
  env:
  - 'TF_VAR_roles=2'
- id: verify additive-local 2 roles again
  waitFor:
  - converge additive-local 2 roles again
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify additive-local']
  env:
  - 'TF_VAR_roles=2'
- id: destroy additive-local
  waitFor:
  - verify additive-local 2 roles again
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do destroy additive-local']
  env:
  - 'TF_VAR_roles=2'


# ----- SUITE authoritative-local

# verify authoritative-local with 2 roles
- id: converge authoritative-local 2 roles
  waitFor:
  - create all
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge authoritative-local']
  env:
  - 'TF_VAR_roles=2'
- id: verify authoritative-local 2 roles
  waitFor:
  - converge authoritative-local 2 roles
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify authoritative-local']
  env:
  - 'TF_VAR_roles=2'

# scale authoritative-local to 1 role and reverify
- id: converge authoritative-local 1 role
  waitFor:
  - verify authoritative-local 2 roles
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge authoritative-local']
  env:
  - 'TF_VAR_roles=1'
- id: verify authoritative-local 1 role
  waitFor:
  - converge authoritative-local 1 role
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify authoritative-local']
  env:
  - 'TF_VAR_roles=1'

# scale authoritative-local back to 2 roles and reverify
- id: converge authoritative-local 2 roles again
  waitFor:
  - verify authoritative-local 1 role
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge authoritative-local']
  env:
  - 'TF_VAR_roles=2'
- id: verify authoritative-local 2 roles again
  waitFor:
  - converge authoritative-local 2 roles again
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify authoritative-local']
  env:
  - 'TF_VAR_roles=2'
- id: destroy authoritative-local
  waitFor:
  - verify authoritative-local 2 roles again
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do destroy authoritative-local']
  env:
  - 'TF_VAR_roles=2'


# ----- SUITE static-and-dynamic

# verify static-and-dynamic-local with 2 roles

- id: converge static-and-dynamic-local 2 roles
  waitFor:
  - create all
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=2'
- id: verify static-and-dynamic-local 2 roles
  waitFor:
  - converge static-and-dynamic-local 2 roles
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=2'

# scale static-and-dynamic-local to 1 role and reverify
- id: converge static-and-dynamic-local 1 role
  waitFor:
  - verify static-and-dynamic-local 2 roles
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=1'
- id: verify static-and-dynamic-local 1 role
  waitFor:
  - converge static-and-dynamic-local 1 role
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=1'

# scale static-and-dynamic-local to 3 roles and reverify
- id: converge static-and-dynamic-local 3 roles
  waitFor:
  - verify static-and-dynamic-local 1 role
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=3'
- id: verify static-and-dynamic-local 3 roles
  waitFor:
  - converge static-and-dynamic-local 3 roles
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=3'

# scale static-and-dynamic-local back to 2 roles and reverify
- id: converge static-and-dynamic-local 2 roles again
  waitFor:
  - verify static-and-dynamic-local 3 roles
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do converge static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=2'
- id: verify static-and-dynamic-local 2 roles again
  waitFor:
  - converge static-and-dynamic-local 2 roles again
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do verify static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=2'
- id: destroy static-and-dynamic-local
  waitFor:
  - verify static-and-dynamic-local 2 roles again
  name: 'gcr.io/cloud-foundation-cicd/$_DOCKER_IMAGE_DEVELOPER_TOOLS:$_DOCKER_TAG_VERSION_DEVELOPER_TOOLS'
  args: ['/bin/bash', '-c', 'source /usr/local/bin/task_helper_functions.sh && kitchen_do destroy static-and-dynamic-local']
  env:
  - 'TF_VAR_roles=2'

tags:
- 'ci'
- 'integration'
substitutions:
  _DOCKER_IMAGE_DEVELOPER_TOOLS: 'cft/developer-tools'
  _DOCKER_TAG_VERSION_DEVELOPER_TOOLS: '1.22'
