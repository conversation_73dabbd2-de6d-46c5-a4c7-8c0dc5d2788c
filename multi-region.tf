# ---------------------------------------------------------------------------------------------------------------------
# MULTI-REGION SUPPORT
# This file defines the region-specific configurations and implements multi-region deployment capability.
# ---------------------------------------------------------------------------------------------------------------------

# ---------------------------------------------------------------------------------------------------------------------
# REGION CONFIGURATION VARIABLES
# Define variables for region-specific configurations
# ---------------------------------------------------------------------------------------------------------------------

variable "primary_region" {
  description = "The primary region for deployment"
  type        = string
  default     = "us-west1"
}

variable "secondary_region" {
  description = "The secondary region for deployment"
  type        = string
  default     = "asia-south1"
}

variable "dr_region" {
  description = "The disaster recovery region for deployment"
  type        = string
  default     = "us-east4"
}

variable "enable_multi_region" {
  description = "Whether to enable multi-region deployment"
  type        = bool
  default     = true
}

variable "enable_dr_region" {
  description = "Whether to enable the disaster recovery region"
  type        = bool
  default     = false
}

# Network CIDR blocks for each region
variable "region_cidrs" {
  description = "CIDR blocks for each region and environment"
  type = object({
    prod = object({
      primary = object({
        subnet_cidr   = string
        pods_cidr     = string
        services_cidr = string
        master_cidr   = string
      })
      secondary = object({
        subnet_cidr   = string
        pods_cidr     = string
        services_cidr = string
        master_cidr   = string
      })
      dr = object({
        subnet_cidr   = string
        pods_cidr     = string
        services_cidr = string
        master_cidr   = string
      })
    })
    nonprod = object({
      primary = object({
        subnet_cidr   = string
        pods_cidr     = string
        services_cidr = string
        master_cidr   = string
      })
      secondary = object({
        subnet_cidr   = string
        pods_cidr     = string
        services_cidr = string
        master_cidr   = string
      })
    })
  })
  default = {
    prod = {
      primary = {
        subnet_cidr   = "10.0.0.0/20"
        pods_cidr     = "10.48.0.0/14"
        services_cidr = "10.52.0.0/20"
        master_cidr   = "172.16.0.0/28"
      }
      secondary = {
        subnet_cidr   = "10.1.0.0/20"
        pods_cidr     = "10.56.0.0/14"
        services_cidr = "10.60.0.0/20"
        master_cidr   = "172.16.0.16/28"
      }
      dr = {
        subnet_cidr   = "10.2.0.0/20"
        pods_cidr     = "10.64.0.0/14"
        services_cidr = "10.68.0.0/20"
        master_cidr   = "172.16.0.32/28"
      }
    }
    nonprod = {
      primary = {
        subnet_cidr   = "10.10.0.0/20"
        pods_cidr     = "10.72.0.0/14"
        services_cidr = "10.76.0.0/20"
        master_cidr   = "172.16.0.48/28"
      }
      secondary = {
        subnet_cidr   = "10.11.0.0/20"
        pods_cidr     = "10.80.0.0/14"
        services_cidr = "10.84.0.0/20"
        master_cidr   = "172.16.0.64/28"
      }
    }
  }
}

# ---------------------------------------------------------------------------------------------------------------------
# REGION-SPECIFIC NETWORK CONFIGURATION
# Configure network resources for each region
# ---------------------------------------------------------------------------------------------------------------------

# Production primary region subnet
resource "google_compute_subnetwork" "prod_primary_subnet" {
  name          = "subnet-prod-primary"
  project       = module.cs-vpc-prod-shared.project_id
  region        = var.primary_region
  network       = module.cs-vpc-prod-shared.network_name
  ip_cidr_range = var.region_cidrs.prod.primary.subnet_cidr

  # Secondary ranges for GKE
  secondary_ip_range {
    range_name    = "gke-pods"
    ip_cidr_range = var.region_cidrs.prod.primary.pods_cidr
  }

  secondary_ip_range {
    range_name    = "gke-services"
    ip_cidr_range = var.region_cidrs.prod.primary.services_cidr
  }

  # Enable flow logs for security
  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling        = 0.5
    metadata             = "INCLUDE_ALL_METADATA"
  }
}

# Production secondary region subnet (only if multi-region is enabled)
resource "google_compute_subnetwork" "prod_secondary_subnet" {
  count = var.enable_multi_region ? 1 : 0

  name          = "subnet-prod-secondary"
  project       = module.cs-vpc-prod-shared.project_id
  region        = var.secondary_region
  network       = module.cs-vpc-prod-shared.network_name
  ip_cidr_range = var.region_cidrs.prod.secondary.subnet_cidr

  # Secondary ranges for GKE
  secondary_ip_range {
    range_name    = "gke-pods"
    ip_cidr_range = var.region_cidrs.prod.secondary.pods_cidr
  }

  secondary_ip_range {
    range_name    = "gke-services"
    ip_cidr_range = var.region_cidrs.prod.secondary.services_cidr
  }

  # Enable flow logs for security
  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling        = 0.5
    metadata             = "INCLUDE_ALL_METADATA"
  }
}

# Production DR region subnet (only if DR region is enabled)
resource "google_compute_subnetwork" "prod_dr_subnet" {
  count = var.enable_dr_region ? 1 : 0

  name          = "subnet-prod-dr"
  project       = module.cs-vpc-prod-shared.project_id
  region        = var.dr_region
  network       = module.cs-vpc-prod-shared.network_name
  ip_cidr_range = var.region_cidrs.prod.dr.subnet_cidr

  # Secondary ranges for GKE
  secondary_ip_range {
    range_name    = "gke-pods"
    ip_cidr_range = var.region_cidrs.prod.dr.pods_cidr
  }

  secondary_ip_range {
    range_name    = "gke-services"
    ip_cidr_range = var.region_cidrs.prod.dr.services_cidr
  }

  # Enable flow logs for security
  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling        = 0.5
    metadata             = "INCLUDE_ALL_METADATA"
  }
}

# Non-production primary region subnet
resource "google_compute_subnetwork" "nonprod_primary_subnet" {
  name          = "subnet-nonprod-primary"
  project       = module.cs-vpc-nonprod-shared.project_id
  region        = var.primary_region
  network       = module.cs-vpc-nonprod-shared.network_name
  ip_cidr_range = var.region_cidrs.nonprod.primary.subnet_cidr

  # Secondary ranges for GKE
  secondary_ip_range {
    range_name    = "gke-pods"
    ip_cidr_range = var.region_cidrs.nonprod.primary.pods_cidr
  }

  secondary_ip_range {
    range_name    = "gke-services"
    ip_cidr_range = var.region_cidrs.nonprod.primary.services_cidr
  }

  # Enable flow logs for security
  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling        = 0.5
    metadata             = "INCLUDE_ALL_METADATA"
  }
}

# Non-production secondary region subnet (only if multi-region is enabled)
resource "google_compute_subnetwork" "nonprod_secondary_subnet" {
  count = var.enable_multi_region ? 1 : 0

  name          = "subnet-nonprod-secondary"
  project       = module.cs-vpc-nonprod-shared.project_id
  region        = var.secondary_region
  network       = module.cs-vpc-nonprod-shared.network_name
  ip_cidr_range = var.region_cidrs.nonprod.secondary.subnet_cidr

  # Secondary ranges for GKE
  secondary_ip_range {
    range_name    = "gke-pods"
    ip_cidr_range = var.region_cidrs.nonprod.secondary.pods_cidr
  }

  secondary_ip_range {
    range_name    = "gke-services"
    ip_cidr_range = var.region_cidrs.nonprod.secondary.services_cidr
  }

  # Enable flow logs for security
  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling        = 0.5
    metadata             = "INCLUDE_ALL_METADATA"
  }
}

# ---------------------------------------------------------------------------------------------------------------------
# MULTI-REGION GKE CLUSTERS
# Create GKE clusters in each deployed region
# ---------------------------------------------------------------------------------------------------------------------

# Production primary region GKE cluster
module "cs-gke-autopilot-prod-primary" {
  source = "./modules/gke_autopilot_cluster"

  project_id             = module.cs-svc-matiks-prod-svc.project_id
  name                   = "gke-autopilot-prod-${var.primary_region}"
  region                 = var.primary_region
  network_self_link      = module.cs-vpc-prod-shared.network_self_link
  subnetwork_self_link   = google_compute_subnetwork.prod_primary_subnet.self_link
  release_channel        = "STABLE"
  description            = "Production GKE Autopilot cluster in primary region (${var.primary_region})"
  pods_ip_range_name     = "gke-pods"
  services_ip_range_name = "gke-services"

  # Enhanced security configuration
  enable_private_nodes                 = true
  enable_private_endpoint              = false
  master_ipv4_cidr_block               = var.region_cidrs.prod.primary.master_cidr
  enable_confidential_nodes            = true
  enable_security_posture              = true
  enable_cost_allocation               = true
  enable_binary_authorization          = true
  binary_authorization_evaluation_mode = "PROJECT_SINGLETON_POLICY_ENFORCE"
  datapath_provider                    = "ADVANCED_DATAPATH"
  deletion_protection                  = false

  # Resource usage export to BigQuery for cost optimization
  enable_resource_usage_export       = true
  resource_usage_bigquery_dataset_id = "gke_resource_usage_prod"
  enable_network_egress_export       = true

  # Use custom service account for nodes
  node_service_account_email = google_service_account.gke_service_account_prod.email

  # Enable Fleet registration
  enable_fleet = true

  # Enhanced monitoring and logging
  monitoring_components     = ["SYSTEM_COMPONENTS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
  logging_components        = ["SYSTEM_COMPONENTS", "WORKLOADS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
  enable_managed_prometheus = true

  # Maintenance window (3 AM UTC)
  maintenance_start_time = "03:00"

  # Master authorized networks (restrict access)
  master_authorized_networks = [
    {
      cidr_block   = var.master_authorized_networks_cidr
      display_name = "Internal VPC"
    }
  ]

  # Use the labels module for consistent labeling
  labels = merge(module.cs-labels-gke-prod.labels, {
    "region" = var.primary_region
    "role"   = "primary"
  })

  depends_on = [
    module.cs-svc-matiks-prod-svc,
    module.cs-vpc-prod-shared,
    google_service_account.gke_service_account_prod,
    google_project_service.container_api_prod,
    google_project_service.iam_api_prod
  ]
}

# Production secondary region GKE cluster (only if multi-region is enabled)
module "cs-gke-autopilot-prod-secondary" {
  count  = var.enable_multi_region ? 1 : 0
  source = "./modules/gke_autopilot_cluster"

  project_id             = module.cs-svc-matiks-prod-svc.project_id
  name                   = "gke-autopilot-prod-${var.secondary_region}"
  region                 = var.secondary_region
  network_self_link      = module.cs-vpc-prod-shared.network_self_link
  subnetwork_self_link   = google_compute_subnetwork.prod_secondary_subnet[0].self_link
  release_channel        = "STABLE"
  description            = "Production GKE Autopilot cluster in secondary region (${var.secondary_region})"
  pods_ip_range_name     = "gke-pods"
  services_ip_range_name = "gke-services"

  # Enhanced security configuration
  enable_private_nodes                 = true
  enable_private_endpoint              = false
  master_ipv4_cidr_block               = var.region_cidrs.prod.secondary.master_cidr
  enable_confidential_nodes            = true
  enable_security_posture              = true
  enable_cost_allocation               = true
  enable_binary_authorization          = true
  binary_authorization_evaluation_mode = "PROJECT_SINGLETON_POLICY_ENFORCE"
  datapath_provider                    = "ADVANCED_DATAPATH"
  deletion_protection                  = false

  # Resource usage export to BigQuery for cost optimization
  enable_resource_usage_export       = true
  resource_usage_bigquery_dataset_id = "gke_resource_usage_prod"
  enable_network_egress_export       = true

  # Use custom service account for nodes
  node_service_account_email = google_service_account.gke_service_account_prod.email

  # Enable Fleet registration
  enable_fleet = true

  # Enhanced monitoring and logging
  monitoring_components     = ["SYSTEM_COMPONENTS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
  logging_components        = ["SYSTEM_COMPONENTS", "WORKLOADS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
  enable_managed_prometheus = true

  # Maintenance window (3 AM UTC)
  maintenance_start_time = "03:00"

  # Master authorized networks (restrict access)
  master_authorized_networks = [
    {
      cidr_block   = var.master_authorized_networks_cidr
      display_name = "Internal VPC"
    }
  ]

  # Use the labels module for consistent labeling
  labels = merge(module.cs-labels-gke-prod.labels, {
    "region" = var.secondary_region
    "role"   = "secondary"
  })

  depends_on = [
    module.cs-svc-matiks-prod-svc,
    module.cs-vpc-prod-shared,
    google_service_account.gke_service_account_prod,
    google_project_service.container_api_prod,
    google_project_service.iam_api_prod
  ]
}

# Non-production primary region GKE cluster
module "cs-gke-autopilot-nonprod-primary" {
  source = "./modules/gke_autopilot_cluster"

  project_id             = module.cs-svc-matiks-nonprod-svc.project_id
  name                   = "gke-autopilot-nonprod-${var.primary_region}"
  region                 = var.primary_region
  network_self_link      = module.cs-vpc-nonprod-shared.network_self_link
  subnetwork_self_link   = google_compute_subnetwork.nonprod_primary_subnet.self_link
  release_channel        = "STABLE"
  description            = "Non-production GKE Autopilot cluster in primary region (${var.primary_region})"
  pods_ip_range_name     = "gke-pods"
  services_ip_range_name = "gke-services"

  # Enhanced security configuration (slightly relaxed for dev environment)
  enable_private_nodes    = true
  enable_private_endpoint = false
  master_ipv4_cidr_block  = var.region_cidrs.nonprod.primary.master_cidr
  enable_security_posture = true
  enable_cost_allocation  = true
  datapath_provider       = "ADVANCED_DATAPATH"
  deletion_protection     = false

  # Resource usage export to BigQuery for cost optimization
  enable_resource_usage_export       = true
  resource_usage_bigquery_dataset_id = "gke_resource_usage_nonprod"
  enable_network_egress_export       = true

  # Use custom service account for nodes
  node_service_account_email = google_service_account.gke_service_account_dev.email

  # Enable Fleet registration
  enable_fleet = true

  # Enhanced monitoring and logging
  monitoring_components     = ["SYSTEM_COMPONENTS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
  logging_components        = ["SYSTEM_COMPONENTS", "WORKLOADS"]
  enable_managed_prometheus = true

  # Maintenance window (2 AM UTC - less critical than production)
  maintenance_start_time = "02:00"

  # Master authorized networks (slightly more permissive for development)
  master_authorized_networks = [
    {
      cidr_block   = var.master_authorized_networks_cidr
      display_name = "Internal VPC"
    }
  ]

  # Use the labels module for consistent labeling
  labels = merge(module.cs-labels-gke-dev.labels, {
    "region" = var.primary_region
    "role"   = "primary"
  })

  depends_on = [
    module.cs-svc-matiks-nonprod-svc,
    module.cs-vpc-nonprod-shared,
    google_service_account.gke_service_account_dev,
    google_project_service.container_api_nonprod,
    google_project_service.iam_api_nonprod
  ]
}

# Non-production secondary region GKE cluster (only if multi-region is enabled)
module "cs-gke-autopilot-nonprod-secondary" {
  count  = var.enable_multi_region ? 1 : 0
  source = "./modules/gke_autopilot_cluster"

  project_id             = module.cs-svc-matiks-nonprod-svc.project_id
  name                   = "gke-autopilot-nonprod-${var.secondary_region}"
  region                 = var.secondary_region
  network_self_link      = module.cs-vpc-nonprod-shared.network_self_link
  subnetwork_self_link   = google_compute_subnetwork.nonprod_secondary_subnet[0].self_link
  release_channel        = "STABLE"
  description            = "Non-production GKE Autopilot cluster in secondary region (${var.secondary_region})"
  pods_ip_range_name     = "gke-pods"
  services_ip_range_name = "gke-services"

  # Enhanced security configuration (slightly relaxed for dev environment)
  enable_private_nodes    = true
  enable_private_endpoint = false
  master_ipv4_cidr_block  = var.region_cidrs.nonprod.secondary.master_cidr
  enable_security_posture = true
  enable_cost_allocation  = true
  datapath_provider       = "ADVANCED_DATAPATH"
  deletion_protection     = false

  # Resource usage export to BigQuery for cost optimization
  enable_resource_usage_export       = true
  resource_usage_bigquery_dataset_id = "gke_resource_usage_nonprod"
  enable_network_egress_export       = true

  # Use custom service account for nodes
  node_service_account_email = google_service_account.gke_service_account_dev.email

  # Enable Fleet registration
  enable_fleet = true

  # Enhanced monitoring and logging
  monitoring_components     = ["SYSTEM_COMPONENTS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
  logging_components        = ["SYSTEM_COMPONENTS", "WORKLOADS"]
  enable_managed_prometheus = true

  # Maintenance window (2 AM UTC - less critical than production)
  maintenance_start_time = "02:00"

  # Master authorized networks (slightly more permissive for development)
  master_authorized_networks = [
    {
      cidr_block   = var.master_authorized_networks_cidr
      display_name = "Internal VPC"
    }
  ]

  # Use the labels module for consistent labeling
  labels = merge(module.cs-labels-gke-dev.labels, {
    "region" = var.secondary_region
    "role"   = "secondary"
  })

  depends_on = [
    module.cs-svc-matiks-nonprod-svc,
    module.cs-vpc-nonprod-shared,
    google_service_account.gke_service_account_dev,
    google_project_service.container_api_nonprod,
    google_project_service.iam_api_nonprod
  ]
}

# ---------------------------------------------------------------------------------------------------------------------
# OUTPUTS
# Export region-specific information
# ---------------------------------------------------------------------------------------------------------------------

output "prod_primary_cluster" {
  description = "The production primary region cluster"
  value = {
    name     = module.cs-gke-autopilot-prod-primary.cluster_name
    region   = module.cs-gke-autopilot-prod-primary.cluster_location
    endpoint = module.cs-gke-autopilot-prod-primary.cluster_endpoint
  }
}

output "prod_secondary_cluster" {
  description = "The production secondary region cluster"
  value = var.enable_multi_region ? {
    name     = module.cs-gke-autopilot-prod-secondary[0].cluster_name
    region   = module.cs-gke-autopilot-prod-secondary[0].cluster_location
    endpoint = module.cs-gke-autopilot-prod-secondary[0].cluster_endpoint
  } : null
}

output "nonprod_primary_cluster" {
  description = "The non-production primary region cluster"
  value = {
    name     = module.cs-gke-autopilot-nonprod-primary.cluster_name
    region   = module.cs-gke-autopilot-nonprod-primary.cluster_location
    endpoint = module.cs-gke-autopilot-nonprod-primary.cluster_endpoint
  }
}

output "nonprod_secondary_cluster" {
  description = "The non-production secondary region cluster"
  value = var.enable_multi_region ? {
    name     = module.cs-gke-autopilot-nonprod-secondary[0].cluster_name
    region   = module.cs-gke-autopilot-nonprod-secondary[0].cluster_location
    endpoint = module.cs-gke-autopilot-nonprod-secondary[0].cluster_endpoint
  } : null
}

output "deployed_regions" {
  description = "The regions that were deployed"
  value = {
    primary   = var.primary_region
    secondary = var.enable_multi_region ? var.secondary_region : null
    dr        = var.enable_dr_region ? var.dr_region : null
  }
}