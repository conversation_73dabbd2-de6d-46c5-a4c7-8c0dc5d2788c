package test

import (
	"testing"
	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

func TestNetworkVpcModule(t *testing.T) {
	t.<PERSON>llel()

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// The path to where our Terraform code is located
		TerraformDir: "../../../examples/network/basic",

		// Variables to pass to our Terraform code using -var options
		Vars: map[string]interface{}{
			"project_id": "test-project",
			"region":     "us-central1",
		},

		// Environment variables to set when running Terraform
		EnvVars: map[string]string{
			"GOOGLE_CLOUD_PROJECT": "test-project",
		},

		// Configure a plan-only test
		PlanOnly: true,
	})

	// Run a Terraform plan and check that it doesn't fail
	terraform.InitAndPlan(t, terraformOptions)

	// Run a Terraform plan and extract the plan
	tfPlanOutput := terraform.Plan(t, terraformOptions)

	// Verify that the plan contains the expected resources
	assert.Contains(t, tfPlanOutput, "google_compute_network.network")
	assert.Contains(t, tfPlanOutput, "google_compute_subnetwork.subnet")
	assert.Contains(t, tfPlanOutput, "google_compute_firewall.firewall")
}

func TestNetworkVpcModuleWithNat(t *testing.T) {
	t.Parallel()

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// The path to where our Terraform code is located
		TerraformDir: "../../../examples/network/nat",

		// Variables to pass to our Terraform code using -var options
		Vars: map[string]interface{}{
			"project_id": "test-project",
			"region":     "us-central1",
		},

		// Environment variables to set when running Terraform
		EnvVars: map[string]string{
			"GOOGLE_CLOUD_PROJECT": "test-project",
		},

		// Configure a plan-only test
		PlanOnly: true,
	})

	// Run a Terraform plan and check that it doesn't fail
	terraform.InitAndPlan(t, terraformOptions)

	// Run a Terraform plan and extract the plan
	tfPlanOutput := terraform.Plan(t, terraformOptions)

	// Verify that the plan contains the expected resources
	assert.Contains(t, tfPlanOutput, "google_compute_network.network")
	assert.Contains(t, tfPlanOutput, "google_compute_subnetwork.subnet")
	assert.Contains(t, tfPlanOutput, "google_compute_router.router")
	assert.Contains(t, tfPlanOutput, "google_compute_router_nat.nat")
}

func TestNetworkVpcModuleWithDns(t *testing.T) {
	t.Parallel()

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// The path to where our Terraform code is located
		TerraformDir: "../../../examples/network/dns",

		// Variables to pass to our Terraform code using -var options
		Vars: map[string]interface{}{
			"project_id": "test-project",
			"region":     "us-central1",
		},

		// Environment variables to set when running Terraform
		EnvVars: map[string]string{
			"GOOGLE_CLOUD_PROJECT": "test-project",
		},

		// Configure a plan-only test
		PlanOnly: true,
	})

	// Run a Terraform plan and check that it doesn't fail
	terraform.InitAndPlan(t, terraformOptions)

	// Run a Terraform plan and extract the plan
	tfPlanOutput := terraform.Plan(t, terraformOptions)

	// Verify that the plan contains the expected resources
	assert.Contains(t, tfPlanOutput, "google_compute_network.network")
	assert.Contains(t, tfPlanOutput, "google_dns_managed_zone.private_zone")
	assert.Contains(t, tfPlanOutput, "google_dns_record_set.a_record")
}
