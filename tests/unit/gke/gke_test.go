package test

import (
	"testing"
	"github.com/gruntwork-io/terratest/modules/terraform"
	"github.com/stretchr/testify/assert"
)

func TestGkeAutopilotModule(t *testing.T) {
	t.<PERSON>lle<PERSON>()

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// The path to where our Terraform code is located
		TerraformDir: "../../../examples/gke/basic",

		// Variables to pass to our Terraform code using -var options
		Vars: map[string]interface{}{
			"project_id": "test-project",
			"region":     "us-central1",
		},

		// Environment variables to set when running Terraform
		EnvVars: map[string]string{
			"GOOGLE_CLOUD_PROJECT": "test-project",
		},

		// Configure a plan-only test
		PlanOnly: true,
	})

	// Run a Terraform plan and check that it doesn't fail
	terraform.InitAndPlan(t, terraformOptions)

	// Run a Terraform plan and extract the plan
	tfPlanOutput := terraform.Plan(t, terraformOptions)

	// Verify that the plan contains the expected resources
	assert.Contains(t, tfPlanOutput, "module.gke.module.autopilot_cluster")
	assert.Contains(t, tfPlanOutput, "google_container_cluster.autopilot_cluster")
	assert.Contains(t, tfPlanOutput, "module.gke.module.monitoring")
	assert.Contains(t, tfPlanOutput, "module.gke.module.security")
}

func TestGkeWithBackupModule(t *testing.T) {
	t.Parallel()

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// The path to where our Terraform code is located
		TerraformDir: "../../../examples/gke/backup",

		// Variables to pass to our Terraform code using -var options
		Vars: map[string]interface{}{
			"project_id": "test-project",
			"region":     "us-central1",
		},

		// Environment variables to set when running Terraform
		EnvVars: map[string]string{
			"GOOGLE_CLOUD_PROJECT": "test-project",
		},

		// Configure a plan-only test
		PlanOnly: true,
	})

	// Run a Terraform plan and check that it doesn't fail
	terraform.InitAndPlan(t, terraformOptions)

	// Run a Terraform plan and extract the plan
	tfPlanOutput := terraform.Plan(t, terraformOptions)

	// Verify that the plan contains the expected resources
	assert.Contains(t, tfPlanOutput, "module.gke.module.autopilot_cluster")
	assert.Contains(t, tfPlanOutput, "google_container_cluster.autopilot_cluster")
	assert.Contains(t, tfPlanOutput, "module.gke.module.backup")
	assert.Contains(t, tfPlanOutput, "google_gke_backup_backup_plan.backup_plan")
}

func TestGkePrivateClusterModule(t *testing.T) {
	t.Parallel()

	// Construct the terraform options with default retryable errors
	terraformOptions := terraform.WithDefaultRetryableErrors(t, &terraform.Options{
		// The path to where our Terraform code is located
		TerraformDir: "../../../examples/gke/private",

		// Variables to pass to our Terraform code using -var options
		Vars: map[string]interface{}{
			"project_id": "test-project",
			"region":     "us-central1",
		},

		// Environment variables to set when running Terraform
		EnvVars: map[string]string{
			"GOOGLE_CLOUD_PROJECT": "test-project",
		},

		// Configure a plan-only test
		PlanOnly: true,
	})

	// Run a Terraform plan and check that it doesn't fail
	terraform.InitAndPlan(t, terraformOptions)

	// Run a Terraform plan and extract the plan
	tfPlanOutput := terraform.Plan(t, terraformOptions)

	// Verify that the plan contains the expected resources
	assert.Contains(t, tfPlanOutput, "module.gke.module.autopilot_cluster")
	assert.Contains(t, tfPlanOutput, "google_container_cluster.autopilot_cluster")
	
	// Verify private cluster configuration
	assert.Contains(t, tfPlanOutput, "private_cluster_config")
	assert.Contains(t, tfPlanOutput, "enable_private_nodes = true")
	assert.Contains(t, tfPlanOutput, "enable_private_endpoint = true")
}
