# ---------------------------------------------------------------------------------------------------------------------
# NETWORK CONFIGURATION
# This file defines the VPC networks, subnets, firewall rules, and Cloud NAT configurations for both 
# production and non-production environments.
# It includes:
# - Shared VPC networks for production and non-production
# - Subnets with private access and flow logs enabled
# - Secondary IP ranges for GKE clusters
# - Firewall rules for basic connectivity
# - Cloud NAT gateways for outbound internet access
# ---------------------------------------------------------------------------------------------------------------------

# Production Shared VPC Network
module "cs-vpc-prod-shared" {
  source  = "terraform-google-modules/network/google"
  version = "~> 11.0"

  project_id   = module.cs-projects.vpc_host_prod_project.project_id
  network_name = "vpc-prod-shared"

  depends_on = [module.cs-projects.vpc_host_prod_project]

  subnets = [
    {
      subnet_name               = "subnet-prod-1"
      subnet_ip                 = var.prod_subnet_cidr
      subnet_region             = "us-west1"
      subnet_private_access     = true
      subnet_flow_logs          = true
      subnet_flow_logs_sampling = "0.5"
      subnet_flow_logs_metadata = "INCLUDE_ALL_METADATA"
      subnet_flow_logs_interval = "INTERVAL_10_MIN"
      secondary_ip_range = [
        {
          range_name    = "gke-pods"
          ip_cidr_range = "********/20"
        },
        {
          range_name    = "gke-services"
          ip_cidr_range = "********/20"
        },
      ]
    },
    {
      subnet_name               = "subnet-prod-2"
      subnet_ip                 = "*********/20"
      subnet_region             = "asia-south1"
      subnet_private_access     = true
      subnet_flow_logs          = true
      subnet_flow_logs_sampling = "0.5"
      subnet_flow_logs_metadata = "INCLUDE_ALL_METADATA"
      subnet_flow_logs_interval = "INTERVAL_10_MIN"
    },
  ]

  firewall_rules = [
    # Default deny all ingress traffic (highest priority)
    {
      name      = "vpc-prod-shared-deny-all-ingress"
      direction = "INGRESS"
      priority  = 65000
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      deny = [
        {
          protocol = "all"
          ports    = []
        }
      ]
      ranges = ["0.0.0.0/0"]
    },

    # Allow internal VPC communication for services
    {
      name      = "vpc-prod-shared-allow-internal"
      direction = "INGRESS"
      priority  = 1000
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = []
        },
        {
          protocol = "udp"
          ports    = []
        }
      ]
      ranges      = [var.prod_subnet_cidr, "*********/20"]
      source_tags = ["internal"]
      target_tags = ["internal"]
    },

    # Allow GKE master to node communication
    {
      name      = "vpc-prod-shared-allow-gke-master"
      direction = "INGRESS"
      priority  = 1001
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["443", "10250", "8443"]
        }
      ]
      ranges      = [var.prod_master_ipv4_cidr_block]
      target_tags = ["gke-node"]
    },

    # Allow health checks from Google Cloud
    {
      name      = "vpc-prod-shared-allow-health-checks"
      direction = "INGRESS"
      priority  = 1002
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["80", "443"]
        }
      ]
      ranges      = ["**********/16", "***********/22"]
      target_tags = ["load-balanced-backend"]
    },

    # Allow ICMP from internal networks only
    {
      name      = "vpc-prod-shared-allow-icmp"
      direction = "INGRESS"
      priority  = 5000
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "icmp"
          ports    = []
        }
      ]
      ranges = [
        var.internal_network_cidr,
      ]
    },

    # Allow SSH via IAP only
    {
      name      = "vpc-prod-shared-allow-ssh"
      direction = "INGRESS"
      priority  = 5001
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["${var.ssh_port}"]
        }
      ]
      ranges = [
        var.iap_cidr_range,
      ]
      target_tags = ["ssh"]
    },

    # Allow RDP via IAP only
    {
      name      = "vpc-prod-shared-allow-rdp"
      direction = "INGRESS"
      priority  = 5002
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["${var.rdp_port}"]
        }
      ]
      ranges = [
        var.iap_cidr_range,
      ]
      target_tags = ["rdp"]
    },
  ]
}

# ---------------------------------------------------------------------------------------------------------------------
# NON-PRODUCTION SHARED VPC NETWORK
# Configure the shared VPC network for non-production environments with subnets and firewall rules
# ---------------------------------------------------------------------------------------------------------------------

# Non-Production Shared VPC Network
module "cs-vpc-nonprod-shared" {
  source  = "terraform-google-modules/network/google"
  version = "~> 11.0"

  project_id   = module.cs-projects.vpc_host_nonprod_project.project_id
  network_name = "vpc-nonprod-shared"

  depends_on = [module.cs-projects.vpc_host_nonprod_project]

  subnets = [
    {
      subnet_name               = "subnet-non-prod-1"
      subnet_ip                 = var.nonprod_subnet_cidr
      subnet_region             = "us-west1"
      subnet_private_access     = true
      subnet_flow_logs          = true
      subnet_flow_logs_sampling = "0.5"
      subnet_flow_logs_metadata = "INCLUDE_ALL_METADATA"
      subnet_flow_logs_interval = "INTERVAL_10_MIN"
    },
    {
      subnet_name               = "subnet-non-prod-2"
      subnet_ip                 = "*********/20"
      subnet_region             = "asia-south1"
      subnet_private_access     = true
      subnet_flow_logs          = true
      subnet_flow_logs_sampling = "0.5"
      subnet_flow_logs_metadata = "INCLUDE_ALL_METADATA"
      subnet_flow_logs_interval = "INTERVAL_10_MIN"
      secondary_ip_range = [
        {
          range_name    = "gke-pods"
          ip_cidr_range = "********/20"
        },
        {
          range_name    = "gke-services"
          ip_cidr_range = "********/20"
        },
      ]
    },
  ]

  firewall_rules = [
    # Default deny all ingress traffic (highest priority)
    {
      name      = "vpc-nonprod-shared-deny-all-ingress"
      direction = "INGRESS"
      priority  = 65000
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      deny = [
        {
          protocol = "all"
          ports    = []
        }
      ]
      ranges = ["0.0.0.0/0"]
    },

    # Allow internal VPC communication for services
    {
      name      = "vpc-nonprod-shared-allow-internal"
      direction = "INGRESS"
      priority  = 1000
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = []
        },
        {
          protocol = "udp"
          ports    = []
        }
      ]
      ranges      = [var.nonprod_subnet_cidr, "*********/20"]
      source_tags = ["internal"]
      target_tags = ["internal"]
    },

    # Allow GKE master to node communication
    {
      name      = "vpc-nonprod-shared-allow-gke-master"
      direction = "INGRESS"
      priority  = 1001
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["443", "10250", "8443"]
        }
      ]
      ranges      = [var.nonprod_master_ipv4_cidr_block]
      target_tags = ["gke-node"]
    },

    # Allow health checks from Google Cloud
    {
      name      = "vpc-nonprod-shared-allow-health-checks"
      direction = "INGRESS"
      priority  = 1002
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["80", "443"]
        }
      ]
      ranges      = ["**********/16", "***********/22"]
      target_tags = ["load-balanced-backend"]
    },

    # Allow development-specific ports (more permissive for non-prod)
    {
      name      = "vpc-nonprod-shared-allow-dev-ports"
      direction = "INGRESS"
      priority  = 1003
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["8080-8090", "9000-9010", "3000-3010"]
        }
      ]
      ranges      = [var.internal_network_cidr]
      target_tags = ["development"]
    },

    # Allow ICMP from internal networks only
    {
      name      = "vpc-nonprod-shared-allow-icmp"
      direction = "INGRESS"
      priority  = 5000
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "icmp"
          ports    = []
        }
      ]
      ranges = [
        var.internal_network_cidr,
      ]
    },

    # Allow SSH via IAP only
    {
      name      = "vpc-nonprod-shared-allow-ssh"
      direction = "INGRESS"
      priority  = 5001
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["${var.ssh_port}"]
        }
      ]
      ranges = [
        var.iap_cidr_range,
      ]
      target_tags = ["ssh"]
    },

    # Allow RDP via IAP only
    {
      name      = "vpc-nonprod-shared-allow-rdp"
      direction = "INGRESS"
      priority  = 5002
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
      allow = [
        {
          protocol = "tcp"
          ports    = ["${var.rdp_port}"]
        }
      ]
      ranges = [
        var.iap_cidr_range,
      ]
      target_tags = ["rdp"]
    },
  ]
}

# ---------------------------------------------------------------------------------------------------------------------
# CLOUD NAT CONFIGURATIONS
# Configure Cloud NAT gateways for each subnet to allow outbound internet access from private instances
# ---------------------------------------------------------------------------------------------------------------------

# Production Cloud NAT for subnet-prod-1 (us-west1)
resource "google_compute_address" "cs-ca-vpc-host-prod-ii997-pc157-subnet-prod-1" {
  project = module.cs-projects.vpc_host_prod_project.project_id
  name    = "ca-vpc-host-prod-ii997-pc157-subnet-prod-1"
  region  = "us-west1"
}

module "cs-cr-vpc-host-prod-ii997-pc157-subnet-prod-1" {
  source  = "terraform-google-modules/cloud-router/google"
  version = "~> 6.3"

  name    = "cr-vpc-host-prod-ii997-pc157-subnet-prod-1"
  project = module.cs-projects.vpc_host_prod_project.project_id
  region  = "us-west1"
  network = module.cs-vpc-prod-shared.network_self_link

  depends_on = [module.cs-vpc-prod-shared]
  nats = [
    {
      name    = "subnet-prod-1"
      nat_ips = google_compute_address.cs-ca-vpc-host-prod-ii997-pc157-subnet-prod-1[*].self_link
      log_config = {
        filter = "TRANSLATIONS_ONLY"
      }
    },
  ]
}

resource "google_compute_address" "cs-ca-vpc-host-prod-ii997-pc157-subnet-prod-2" {
  project = module.cs-projects.vpc_host_prod_project.project_id
  name    = "ca-vpc-host-prod-ii997-pc157-subnet-prod-2"
  region  = "asia-south1"
}

module "cs-cr-vpc-host-prod-ii997-pc157-subnet-prod-2" {
  source  = "terraform-google-modules/cloud-router/google"
  version = "~> 6.3"

  name    = "cr-vpc-host-prod-ii997-pc157-subnet-prod-2"
  project = module.cs-projects.vpc_host_prod_project.project_id
  region  = "asia-south1"
  network = module.cs-vpc-prod-shared.network_self_link

  depends_on = [module.cs-vpc-prod-shared]
  nats = [
    {
      name    = "subnet-prod-2"
      nat_ips = google_compute_address.cs-ca-vpc-host-prod-ii997-pc157-subnet-prod-2[*].self_link
      log_config = {
        filter = "TRANSLATIONS_ONLY"
      }
    },
  ]
}

resource "google_compute_address" "cs-ca-vpc-host-nonprod-hr624-wc998-subnet-non-prod-1" {
  project = module.cs-projects.vpc_host_nonprod_project.project_id
  name    = "ca-vpc-host-nonprod-hr624-wc998-subnet-non-prod-1"
  region  = "us-west1"
}

module "cs-cr-vpc-host-nonprod-hr624-wc998-subnet-non-prod-1" {
  source  = "terraform-google-modules/cloud-router/google"
  version = "~> 6.3"

  name    = "cr-vpc-host-nonprod-hr624-wc998-subnet-non-prod-1"
  project = module.cs-projects.vpc_host_nonprod_project.project_id
  region  = "us-west1"
  network = module.cs-vpc-nonprod-shared.network_self_link

  depends_on = [module.cs-vpc-nonprod-shared]
  nats = [
    {
      name    = "subnet-non-prod-1"
      nat_ips = google_compute_address.cs-ca-vpc-host-nonprod-hr624-wc998-subnet-non-prod-1[*].self_link
      log_config = {
        filter = "TRANSLATIONS_ONLY"
      }
    },
  ]
}

resource "google_compute_address" "cs-ca-vpc-host-nonprod-hr624-wc998-subnet-non-prod-2" {
  project = module.cs-projects.vpc_host_nonprod_project.project_id
  name    = "ca-vpc-host-nonprod-hr624-wc998-subnet-non-prod-2"
  region  = "asia-south1"
}

module "cs-cr-vpc-host-nonprod-hr624-wc998-subnet-non-prod-2" {
  source  = "terraform-google-modules/cloud-router/google"
  version = "~> 6.3"

  name    = "cr-vpc-host-nonprod-hr624-wc998-subnet-non-prod-2"
  project = module.cs-projects.vpc_host_nonprod_project.project_id
  region  = "asia-south1"
  network = module.cs-vpc-nonprod-shared.network_self_link

  depends_on = [module.cs-vpc-nonprod-shared]
  nats = [
    {
      name    = "subnet-non-prod-2"
      nat_ips = google_compute_address.cs-ca-vpc-host-nonprod-hr624-wc998-subnet-non-prod-2[*].self_link
      log_config = {
        filter = "TRANSLATIONS_ONLY"
      }
    },
  ]
}
