package templates.gcp.NetworkSecurityPolicy

import data.validator.gcp.lib as lib

deny[{
  "msg": message,
  "details": metadata,
}] {
  constraint := input.constraint
  lib.get_constraint_params(constraint, params)

  asset := input.asset
  asset.asset_type == "compute.googleapis.com/Firewall"

  firewall := asset.resource.data

  # Check for overly permissive firewall rules
  firewall.sourceRanges[_] == "0.0.0.0/0"

  # Check if the rule allows all traffic
  allowed := firewall.allowed[_]
  allowed.IPProtocol == "all"

  message := sprintf("Firewall rule %v allows all traffic from the internet", [asset.name])
  metadata := {"resource": asset.name}
}
