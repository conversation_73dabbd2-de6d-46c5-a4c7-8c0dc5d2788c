# nonprod Environment

This directory contains the Terraform configuration for the nonprod environment.

## Resources

- VPC Network with subnets, firewall rules, and NAT
- Host project for Shared VPC
- Service project for GKE
- GKE Autopilot cluster with private nodes
- Logging and monitoring configuration
- Storage resources (Cloud Storage buckets and BigQuery datasets)

## Usage

1. Copy the example variables file and update it with your values:

```bash
cp terraform.tfvars.example terraform.tfvars
```

2. Initialize Terraform with the backend configuration:

```bash
terraform init -backend-config=../../backends/nonprod.tfbackend
```

3. Plan the changes:

```bash
terraform plan -out=plan.out
```

4. Apply the changes:

```bash
terraform apply plan.out
```

## Backend Configuration

Create a file named `nonprod.tfbackend` in the `backends` directory with the following content:

```
bucket  = "my-terraform-state-bucket"
prefix  = "terraform/state/nonprod"
```

## Notes

- This environment uses a Shared VPC architecture with a host project and service project
- The GKE cluster is configured with private nodes for enhanced security
- Logging is configured to export all logs to Cloud Storage
- Monitoring is configured with dashboards and alerts for the GKE cluster
