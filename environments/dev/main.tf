/**
 * # dev Environment
 *
 * This is the main configuration file for the dev environment.
 */

# Configure the providers
provider "google" {
  project = var.project_id
  region  = var.region
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
}

# Create the network
module "network" {
  source = "../../modules/network"

  project_id = var.project_id
  name       = "vpc-dev"
  
  # VPC configuration
  auto_create_subnetworks = false
  routing_mode            = "GLOBAL"
  mtu                     = 1460
  description             = "dev VPC network"
  
  # Subnet configuration
  subnets = [
    {
      name          = "subnet-dev-1"
      ip_cidr_range = var.subnet_cidr
      region        = var.region
      description   = "dev subnet"
      private_access = true
      secondary_ip_ranges = [
        {
          range_name    = "gke-pods"
          ip_cidr_range = var.subnet_pods_cidr
        },
        {
          range_name    = "gke-services"
          ip_cidr_range = var.subnet_services_cidr
        }
      ]
    }
  ]
  
  # Firewall configuration
  firewall_rules = [
    {
      name        = "vpc-allow-internal"
      description = "Allow internal traffic"
      direction   = "INGRESS"
      priority    = 1000
      source_ranges = [var.subnet_cidr]
      allow = [
        {
          protocol = "tcp"
          ports    = ["0-65535"]
        },
        {
          protocol = "udp"
          ports    = ["0-65535"]
        },
        {
          protocol = "icmp"
        }
      ]
    },
    {
      name        = "vpc-allow-ssh-iap"
      description = "Allow SSH via IAP"
      direction   = "INGRESS"
      priority    = 5000
      source_ranges = ["************/20"]
      target_tags = ["ssh"]
      allow = [
        {
          protocol = "tcp"
          ports    = ["22"]
        }
      ]
    },
    {
      name        = "vpc-deny-all-ingress"
      description = "Deny all ingress traffic"
      direction   = "INGRESS"
      priority    = 65000
      deny = [
        {
          protocol = "all"
        }
      ]
    }
  ]
  
  # NAT configuration
  nat_config = {
    region                             = var.region
    nat_ip_allocate_option             = "AUTO_ONLY"
    source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
    log_config = {
      enable = true
      filter = "ERRORS_ONLY"
    }
  }
  
  # Labels
  labels = {
    environment = "dev"
    managed-by  = "terraform"
  }
}

# Create the host project
module "host_project" {
  source = "../../modules/project"

  name            = "vpc-host-dev"
  project_id      = var.host_project_id
  org_id          = var.org_id
  folder_id       = var.folder_id
  billing_account = var.billing_account
  
  # Project type
  project_type    = "host"
  environment     = "dev"
  
  # APIs to enable
  activate_apis = [
    "compute.googleapis.com",
    "container.googleapis.com",
    "servicenetworking.googleapis.com",
    "dns.googleapis.com",
    "serviceusage.googleapis.com"
  ]
  
  # Labels
  labels = {
    environment = "dev"
    managed-by  = "terraform"
  }
}

# Create the service project
module "service_project" {
  source = "../../modules/project"

  name            = "gke-dev"
  project_id      = var.service_project_id
  org_id          = var.org_id
  folder_id       = var.folder_id
  billing_account = var.billing_account
  
  # Project type
  project_type    = "service"
  environment     = "dev"
  host_project_id = module.host_project.project_id
  
  # APIs to enable
  activate_apis = [
    "compute.googleapis.com",
    "container.googleapis.com",
    "serviceusage.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com",
    "stackdriver.googleapis.com",
    "secretmanager.googleapis.com"
  ]
  
  # Labels
  labels = {
    environment = "dev"
    managed-by  = "terraform"
  }
  
  depends_on = [module.host_project]
}

# Create the GKE cluster
module "gke" {
  source = "../../modules/gke"

  project_id           = module.service_project.project_id
  name                 = "gke-dev-cluster"
  region               = var.region
  network_self_link    = module.network.network_self_link
  subnetwork_self_link = module.network.subnet_self_links["subnet-dev-1"]
  
  # Environment configuration
  environment = "dev"
  
  # Network configuration
  pods_ip_range_name     = "gke-pods"
  services_ip_range_name = "gke-services"
  
  # Private cluster configuration
  enable_private_nodes    = true
  enable_private_endpoint = false
  master_ipv4_cidr_block  = var.gke_master_ipv4_cidr_block
  
  # Security configuration
  enable_confidential_nodes = true
  enable_security_posture   = true
  
  # Monitoring configuration
  enable_managed_prometheus = true
  monitoring_components     = ["SYSTEM_COMPONENTS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
  logging_components        = ["SYSTEM_COMPONENTS", "WORKLOADS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
  
  # Service account configuration
  service_account_create = true
  service_account_name   = "gke-dev-sa"
  
  # Labels
  labels = {
    environment = "dev"
    managed-by  = "terraform"
  }
  
  depends_on = [module.service_project, module.network]
}

# Configure logging
module "logging" {
  source = "../../modules/logging"

  project_id = module.service_project.project_id
  
  # Log export configuration
  log_exports = [
    {
      name        = "all-logs-to-storage"
      description = "Export all logs to Cloud Storage"
      filter      = ""
      destination = "storage"
      destination_resource = {
        name     = "${module.service_project.project_id}-logs-bucket"
        location = var.region
      }
    }
  ]
  
  # Log metric configuration
  log_metrics = [
    {
      name        = "error-count"
      description = "Count of error logs"
      filter      = "severity >= ERROR"
      metric_descriptor = {
        metric_kind = "DELTA"
        value_type  = "INT64"
      }
    }
  ]
  
  # Log retention configuration
  log_retention = {
    retention_days = 90
    bucket_name    = "dev-logs"
    bucket_location = "global"
    description    = "dev log bucket with 90-day retention"
  }
  
  depends_on = [module.service_project]
}

# Configure monitoring
module "monitoring" {
  source = "../../modules/monitoring"

  project_id = module.service_project.project_id
  
  # Dashboard configuration
  dashboards = [
    {
      display_name = "GKE dev Dashboard"
      layout_type  = "mosaic"
      widgets = [
        {
          title       = "CPU Utilization"
          chart_type  = "line"
          metric_type = "kubernetes.io/node/cpu/allocatable_utilization"
          resource_type = "k8s_node"
          group_by_fields = ["cluster_name"]
          width       = 6
          height      = 4
        },
        {
          title       = "Memory Utilization"
          chart_type  = "line"
          metric_type = "kubernetes.io/node/memory/allocatable_utilization"
          resource_type = "k8s_node"
          group_by_fields = ["cluster_name"]
          width       = 6
          height      = 4
        },
        {
          title       = "Pod Count"
          chart_type  = "stacked"
          metric_type = "kubernetes.io/pod/status/phase_count"
          resource_type = "k8s_cluster"
          group_by_fields = ["phase"]
          width       = 12
          height      = 4
        },
        {
          title       = "Error Logs"
          chart_type  = "logs"
          filter      = "resource.type=\"k8s_cluster\" AND severity>=ERROR"
          width       = 12
          height      = 4
        }
      ]
    }
  ]
  
  # Alert policy configuration
  alert_policies = [
    {
      display_name = "High CPU Utilization Alert"
      conditions = [
        {
          display_name = "CPU Utilization > 80%"
          condition_threshold = {
            filter          = "resource.type = \"k8s_node\" AND metric.type = \"kubernetes.io/node/cpu/allocatable_utilization\""
            duration        = "300s"
            comparison      = "COMPARISON_GT"
            threshold_value = 0.8
            aggregations = [
              {
                alignment_period   = "60s"
                per_series_aligner = "ALIGN_MEAN"
              }
            ]
          }
        }
      ]
      notification_channels = ["email-channel"]
      documentation = {
        content   = "High CPU utilization detected on GKE nodes. This may indicate a need to scale up the cluster or optimize workloads."
        mime_type = "text/markdown"
      }
    }
  ]
  
  # Notification channel configuration
  notification_channels = [
    {
      display_name = "Email Notification Channel"
      type         = "email"
      labels = {
        email_address = var.alert_email
      }
    }
  ]
  
  depends_on = [module.service_project, module.gke]
}

# Configure storage
module "storage" {
  source = "../../modules/storage"

  project_id = module.service_project.project_id
  
  # Cloud Storage bucket configuration
  buckets = [
    {
      name          = "${module.service_project.project_id}-assets"
      location      = var.region
      storage_class = "STANDARD"
      versioning    = true
      
      lifecycle_rules = [
        {
          action = {
            type = "Delete"
          }
          condition = {
            age = 365
          }
        }
      ]
      
      labels = {
        environment = "dev"
        managed-by  = "terraform"
      }
    }
  ]
  
  # BigQuery dataset configuration
  bigquery_datasets = [
    {
      dataset_id    = "analytics"
      friendly_name = "Analytics Dataset"
      description   = "Dataset for analytics data"
      location      = "US"
      
      access = [
        {
          role          = "OWNER"
          special_group = "projectOwners"
        },
        {
          role          = "READER"
          special_group = "projectReaders"
        }
      ]
      
      labels = {
        environment = "dev"
        managed-by  = "terraform"
      }
    }
  ]
  
  depends_on = [module.service_project]
}
