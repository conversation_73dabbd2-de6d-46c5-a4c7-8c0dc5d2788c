/**
 * Copyright 2023 Matiks
 *
 * Terraform and provider version constraints.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = ">= 4.80.0"
    }
  }
  
  backend "gcs" {
    # Backend configuration is provided via -backend-config
    # when running terraform init
  }
}
