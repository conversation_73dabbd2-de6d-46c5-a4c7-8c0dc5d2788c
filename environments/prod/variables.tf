/**
 * Copyright 2023 Matiks
 *
 * Variables for the production environment.
 */

variable "project_id" {
  description = "The ID of the project where resources will be created"
  type        = string
}

variable "host_project_id" {
  description = "The ID of the host project"
  type        = string
}

variable "service_project_id" {
  description = "The ID of the service project"
  type        = string
}

variable "org_id" {
  description = "The organization ID"
  type        = string
}

variable "folder_id" {
  description = "The folder ID where projects will be created"
  type        = string
}

variable "billing_account" {
  description = "The billing account ID"
  type        = string
}

variable "region" {
  description = "The region where resources will be created"
  type        = string
  default     = "us-west1"
}

variable "subnet_prod_1_cidr" {
  description = "The CIDR range for the production subnet"
  type        = string
  default     = "10.0.0.0/24"
}

variable "subnet_prod_1_pods_cidr" {
  description = "The CIDR range for GKE pods in the production subnet"
  type        = string
  default     = "********/16"
}

variable "subnet_prod_1_services_cidr" {
  description = "The CIDR range for GKE services in the production subnet"
  type        = string
  default     = "********/20"
}

variable "gke_master_ipv4_cidr_block" {
  description = "The IP range in CIDR notation for the GKE master network"
  type        = string
  default     = "**********/28"
}

variable "alert_email" {
  description = "The email address to send alerts to"
  type        = string
}

variable "enable_apis" {
  description = "Whether to enable APIs in the projects"
  type        = bool
  default     = true
}
