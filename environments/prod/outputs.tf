/**
 * Copyright 2023 Matiks
 *
 * Outputs for the production environment.
 */

output "network_id" {
  description = "The ID of the VPC network"
  value       = module.network.network_id
}

output "network_self_link" {
  description = "The self-link of the VPC network"
  value       = module.network.network_self_link
}

output "subnet_ids" {
  description = "The IDs of the subnets"
  value       = module.network.subnet_ids
}

output "host_project_id" {
  description = "The ID of the host project"
  value       = module.host_project.project_id
}

output "service_project_id" {
  description = "The ID of the service project"
  value       = module.service_project.project_id
}

output "gke_cluster_name" {
  description = "The name of the GKE cluster"
  value       = module.gke.cluster_name
}

output "gke_cluster_endpoint" {
  description = "The endpoint of the GKE cluster"
  value       = module.gke.cluster_endpoint
  sensitive   = true
}

output "gke_service_account_email" {
  description = "The email of the GKE service account"
  value       = module.gke.service_account_email
}

output "storage_bucket_names" {
  description = "The names of the storage buckets"
  value       = module.storage.bucket_names
}

output "bigquery_dataset_ids" {
  description = "The IDs of the BigQuery datasets"
  value       = module.storage.bigquery_dataset_ids
}

output "dashboard_urls" {
  description = "The URLs of the monitoring dashboards"
  value       = { for name, dashboard in module.monitoring.dashboards : name => dashboard.dashboard_url }
}
