# In order to create google groups, the calling identity should have at least the
# Group Admin role in Google Admin. More info: https://support.google.com/a/answer/2405986

module "cs-gg-matiks-prod-svc" {
  source  = "terraform-google-modules/group/google"
  version = "~> 0.7"

  id           = "<EMAIL>"
  display_name = "matiks-prod-svc"
  customer_id  = data.google_organization.org.directory_customer_id
  types = [
    "default",
    "security",
  ]
}

module "cs-gg-matiks-nonprod-svc" {
  source  = "terraform-google-modules/group/google"
  version = "~> 0.7"

  id           = "<EMAIL>"
  display_name = "matiks-nonprod-svc"
  customer_id  = data.google_organization.org.directory_customer_id
  types = [
    "default",
    "security",
  ]
}

module "cs-gg-matiks-ds-prod-svc" {
  source  = "terraform-google-modules/group/google"
  version = "~> 0.7"

  id           = "<EMAIL>"
  display_name = "matiks-ds-prod-svc"
  customer_id  = data.google_organization.org.directory_customer_id
  types = [
    "default",
    "security",
  ]
}

module "cs-gg-matiks-ds-nonprod-svc" {
  source  = "terraform-google-modules/group/google"
  version = "~> 0.7"

  id           = "<EMAIL>"
  display_name = "matiks-ds-nonprod-svc"
  customer_id  = data.google_organization.org.directory_customer_id
  types = [
    "default",
    "security",
  ]
}
