#!/bin/bash

# This script runs tests for the Terraform code

# Exit on error
set -e

# Check if go is installed
if ! command -v go &> /dev/null; then
  echo "Error: go is not installed"
  echo "Please install go: https://golang.org/doc/install"
  exit 1
fi

# Check if ruby is installed
if ! command -v ruby &> /dev/null; then
  echo "Error: ruby is not installed"
  echo "Please install ruby: https://www.ruby-lang.org/en/documentation/installation/"
  exit 1
fi

# Check if bundle is installed
if ! command -v bundle &> /dev/null; then
  echo "Error: bundle is not installed"
  echo "Please install bundler: gem install bundler"
  exit 1
fi

# Get the root directory of the repository
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR="$SCRIPT_DIR/.."

# Run unit tests
echo "Running unit tests..."
cd "$ROOT_DIR/tests/unit"
go test -v ./...

# Run integration tests
echo "Running integration tests..."
cd "$ROOT_DIR/tests/integration/basic-infrastructure"
bundle exec kitchen test

echo "Tests completed successfully!"
