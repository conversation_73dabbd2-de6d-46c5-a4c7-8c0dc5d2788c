#!/bin/bash

# This script initializes a Terraform environment with the appropriate backend configuration

# Check if environment is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <environment>"
  echo "Example: $0 prod"
  exit 1
fi

ENV=$1
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR="$SCRIPT_DIR/.."
ENV_DIR="$ROOT_DIR/environments/$ENV"
BACKEND_FILE="$ROOT_DIR/backends/$ENV.tfbackend"

# Check if environment directory exists
if [ ! -d "$ENV_DIR" ]; then
  echo "Error: Environment directory $ENV_DIR does not exist"
  exit 1
fi

# Check if backend file exists
if [ ! -f "$BACKEND_FILE" ]; then
  echo "Error: Backend file $BACKEND_FILE does not exist"
  echo "Please create it from the example file:"
  echo "cp $BACKEND_FILE.example $BACKEND_FILE"
  exit 1
fi

# Initialize Terraform
cd "$ENV_DIR"
terraform init -backend-config="$BACKEND_FILE"

echo "Environment $ENV initialized successfully"
