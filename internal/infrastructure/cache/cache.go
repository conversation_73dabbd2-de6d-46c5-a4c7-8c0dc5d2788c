package cache

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
)

type Cache interface {
	Get(ctx context.Context, key string) ([]byte, error)
	Set(ctx context.Context, key string, value []byte, expiration time.Duration) error
	Incr(ctx context.Context, key string) (int64, error)
	Decr(ctx context.Context, key string) (int64, error)
	Delete(ctx context.Context, key string) error
	DeleteMany(ctx context.Context, keys ...string) error
	ClearAll(ctx context.Context) error
	GetAll(ctx context.Context, pattern string) (map[string][]byte, error)
	DeleteAll(ctx context.Context, pattern string) error
	HSet(ctx context.Context, key, field string, value interface{}) error
	HGetAll(ctx context.Context, key string) (map[string]string, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error
	ZAdd(ctx context.Context, key string, members ...redis.Z) error
	ZRemRang<PERSON><PERSON>y<PERSON>core(ctx context.Context, key, min, max string) error
	ZCount(ctx context.Context, key, min, max string) (int64, error)
	ZRevRangeWithScores(ctx context.Context, key string, start, stop int64) ([]redis.Z, error)
	ZScore(ctx context.Context, key, member string) (float64, error)
	ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error)
	ZCard(ctx context.Context, key string) (int64, error)
	ZRem(ctx context.Context, key, entry string) error
	MGet(ctx context.Context, keys ...string) ([]interface{}, error)
}
