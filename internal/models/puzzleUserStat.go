package models

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PuzzleUserStats struct {
	ID              primitive.ObjectID `json:"_id,omitempty" bson:"_id,omitempty"`
	UserID          primitive.ObjectID `json:"userId" bson:"userId"`
	BestTime        *int               `json:"bestTime" bson:"bestTime"`
	NumOfSubmission *int               `json:"numOfSubmission" bson:"numOfSubmission"`
	AverageTime     *int               `json:"averageTime" bson:"averageTime"`
}
