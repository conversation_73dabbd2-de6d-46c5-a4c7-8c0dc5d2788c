package puzzle

import (
	"context"
	"errors"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetUserPuzzleStats(ctx context.Context) (*models.PuzzleUserStats, error) {
	userId, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	userPuzzleStat, err := s.puzzleUserStatRepo.FindByUserID(ctx, userId)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil // Or perhaps return a default PuzzleUserStat{}
		}
		return nil, err
	}

	return userPuzzleStat, nil
}
