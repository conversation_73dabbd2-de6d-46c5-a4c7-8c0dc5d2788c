package game

import (
	"context"
	"fmt"
	"math"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/userStreak"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils/rankingutils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

type ratingChangeParams struct {
	winner, loser                    *models.User
	winnerPoints, loserPoints        *float64
	maxScoreDifference               int
	kFactor                          float64
	minLooserChange, maxWinnerChange int
}

func (s *service) EndAbilityDuelsGame(ctx context.Context, gameID *primitive.ObjectID) (*models.Game, error) {
	game, err := s.GetGameByID(ctx, gameID)
	if err != nil {
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	if game == nil {
		return nil, fmt.Errorf("game not found")
	}

	zlog.Debug(ctx, "Game end event", zap.String("gameID", gameID.Hex()))
	minValueForLooserRatingChange, maxValueForWinnerRatingChange := gameutils.GetMinMaxValueForRatingChange(game.GameType)

	gameStatus := game.GameStatus
	questions := game.Questions
	startTime := game.StartTime

	if gameStatus != models.GameStatusStarted {
		if gameStatus == models.GameStatusCancelled {
			game.GameStatus = models.GameStatusEnded
			return game, nil
		}

		zlog.Info(ctx, "Game is not in a started state, skipping end game")
		return nil, nil
	}

	game.GameStatus = models.GameStatusEnded
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}
	sortedLeaderboard := gameutils.UpdateLeaderboardRanks(game)

	if len(sortedLeaderboard) < 2 {
		return nil, fmt.Errorf("not enough players")
	}

	winnerID := sortedLeaderboard[0].UserID
	loserID := sortedLeaderboard[1].UserID

	if winnerID == nil || loserID == nil {
		return nil, fmt.Errorf("invalid player IDs")
	}

	winner, err := s.userService.GetUserByID(ctx, *winnerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get winner: %w", err)
	}

	loser, err := s.userService.GetUserByID(ctx, *loserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get loser: %w", err)
	}

	if winner == nil || loser == nil {
		return nil, fmt.Errorf("players not found")
	}

	winnerDefaultRating := gameutils.GetPlayerRatingByGameType(winner, models.GameTypeAbilityDuels)
	loserDefaultRating := gameutils.GetPlayerRatingByGameType(loser, models.GameTypeAbilityDuels)

	if winner.RatingV2 == nil {
		winner.RatingV2 = &models.UserRating{
			GlobalRating:       winner.Rating,
			AbilityDuelsRating: utils.AllocPtr(winnerDefaultRating),
		}
	}

	if winner.RatingV2 != nil && winner.RatingV2.AbilityDuelsRating == nil {
		winner.RatingV2.AbilityDuelsRating = utils.AllocPtr(winnerDefaultRating)
	}

	if loser.RatingV2 == nil {
		loser.RatingV2 = &models.UserRating{
			GlobalRating:       loser.Rating,
			AbilityDuelsRating: utils.AllocPtr(loserDefaultRating),
		}
	}

	if loser.RatingV2 != nil && loser.RatingV2.AbilityDuelsRating == nil {
		loser.RatingV2.AbilityDuelsRating = utils.AllocPtr(loserDefaultRating)
	}

	maxScoreDifference := 0
	for _, questionObj := range questions {
		if questionObj.Question.Rating != nil && len(questionObj.Submissions) > 0 {
			maxScoreDifference += 4
		}
	}

	ratingParams := &ratingChangeParams{
		winner:             winner,
		loser:              loser,
		winnerPoints:       sortedLeaderboard[0].TotalPoints,
		loserPoints:        sortedLeaderboard[1].TotalPoints,
		maxScoreDifference: maxScoreDifference,
		kFactor:            gameutils.GetKFactor(game.Config),
		minLooserChange:    minValueForLooserRatingChange,
		maxWinnerChange:    maxValueForWinnerRatingChange,
	}
	winnerRatingChange, loserRatingChange := calculateRatingChanges(ratingParams)

	winnerHighestRating := int(math.Max(float64(*winner.RatingV2.AbilityDuelsRating), float64(*winner.RatingV2.AbilityDuelsRating+winnerRatingChange)))
	if winner.Stats != nil {
		winnerHighestRating = int(math.Max(float64(winnerHighestRating), float64(winner.Stats.Hr)))
	}

	loserHighestRating := int(math.Max(float64(*loser.RatingV2.AbilityDuelsRating), float64(*loser.RatingV2.AbilityDuelsRating+loserRatingChange)))
	if loser.Stats != nil {
		loserHighestRating = int(math.Max(float64(loserHighestRating), float64(loser.Stats.Hr)))
	}

	winnerGame := &models.UserGame{
		ID:         utils.AllocPtr(gameID.Hex()),
		ST:         startTime,
		IsWinner:   utils.AllocPtr(true),
		OpponentID: loserID,
	}

	loserGame := &models.UserGame{
		ID:         utils.AllocPtr(gameID.Hex()),
		ST:         startTime,
		IsWinner:   utils.AllocPtr(false),
		OpponentID: winnerID,
	}

	success, err := s.userGameBucketRepo.AddGame(ctx, winner.ID, winnerGame)
	if err != nil || !success {
		return nil, err
	}
	success, err = s.userGameBucketRepo.AddGame(ctx, loser.ID, loserGame)
	if err != nil || !success {
		return nil, err
	}

	gameutils.UpdateUserStats(winner, loser, winnerHighestRating, winnerGame)
	gameutils.UpdateUserStats(loser, winner, loserHighestRating, loserGame)

	*winner.RatingV2.AbilityDuelsRating += winnerRatingChange
	*loser.RatingV2.AbilityDuelsRating += loserRatingChange

	err = botutils.UpdateHumanBotCache(winner, s.cache, false)
	if err != nil {
		return nil, err
	}
	err = botutils.UpdateHumanBotCache(loser, s.cache, false)
	if err != nil {
		return nil, err
	}

	err = s.userService.UpdateUserFromObject(ctx, winner)
	if err != nil {
		return nil, fmt.Errorf("failed to update winner: %w", err)
	}

	err = s.userService.UpdateUserFromObject(ctx, loser)
	if err != nil {
		return nil, fmt.Errorf("failed to update loser: %w", err)
	}

	sortedLeaderboard[0].RatingChange = utils.AllocPtr(winnerRatingChange)
	sortedLeaderboard[1].RatingChange = utils.AllocPtr(loserRatingChange)

	sortedLeaderboard[0].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForWinner(sortedLeaderboard[0])
	sortedLeaderboard[1].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForLoser(sortedLeaderboard[1])

	botUsers := s.botDetectionService.HandleEndGameBotDetection(ctx, game, *winnerID, *loserID)

	if len(botUsers) > 0 {
		if botUsers[winner.ID] {
			loserRatingChange = -1
			sortedLeaderboard[1].RatingChange = utils.AllocPtr(loserRatingChange)
			zlog.Info(ctx, "Limited loser rating change due to bot winner in ability duels",
				zap.String("winnerID", winnerID.Hex()),
				zap.String("loserID", loserID.Hex()),
				zap.Int("ratingChange", loserRatingChange))
		}

		if botUsers[loser.ID] {
			winnerRatingChange = 1
			sortedLeaderboard[0].RatingChange = utils.AllocPtr(winnerRatingChange)
			zlog.Info(ctx, "Limited winner rating change due to bot loser in ability duels",
				zap.String("winnerID", winnerID.Hex()),
				zap.String("loserID", loserID.Hex()),
				zap.Int("ratingChange", winnerRatingChange))
		}
	}

	game.LeaderBoard = sortedLeaderboard
	game.GameStatus = models.GameStatusEnded

	if game.GameType != models.GameTypeFastestFinger && game.GameType != models.GameTypeFlashAnzan {
		gameutils.SaveMinifiedQuestions(game)
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_ENDED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}

	game.RematchRequestedBy = nil

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	s.triggerAsyncUpdates(ctx, game, winner, loser, sortedLeaderboard)

	return game, nil
}

func calculateRatingChanges(params *ratingChangeParams) (winnerChange, loserChange int) {
	expectedWinnerScore := 1 / (1 + math.Pow(10, (float64(*params.loser.RatingV2.AbilityDuelsRating)-float64(*params.winner.RatingV2.AbilityDuelsRating))/400))
	expectedLoserScore := 1 / (1 + math.Pow(10, (float64(*params.winner.RatingV2.AbilityDuelsRating)-float64(*params.loser.RatingV2.AbilityDuelsRating))/400))

	hasSomeoneNotSubmitted := *params.winnerPoints == 0 || *params.loserPoints == 0
	isTie := *params.winnerPoints == *params.loserPoints
	bothNotSubmitted := hasSomeoneNotSubmitted && isTie

	if !hasSomeoneNotSubmitted {
		scoreDiff := math.Abs(float64(*params.winnerPoints - *params.loserPoints))
		normalizedDiff := math.Min(math.Max(scoreDiff/math.Max(float64(params.maxScoreDifference), 1), 0), 1)
		adjustmentFactor := 0.2 + 1.6*normalizedDiff

		if isTie {
			winnerChange = int(math.Max(1, float64(params.kFactor)*(0.5-expectedWinnerScore)*adjustmentFactor))
			loserChange = int(math.Min(-1, float64(params.kFactor)*(0.5-expectedLoserScore)*adjustmentFactor))
		} else {
			winnerChange = int(math.Max(1, float64(params.kFactor)*(1-expectedWinnerScore)*adjustmentFactor))
			loserChange = int(math.Min(-1, float64(params.kFactor)*(0-expectedLoserScore)*adjustmentFactor))
		}
	} else {
		winnerChange = 0
		loserChange = -2
		if bothNotSubmitted {
			loserChange = 0
		}
	}

	winnerChange = int(math.Min(float64(winnerChange), float64(params.maxWinnerChange)))
	loserChange = int(math.Max(float64(loserChange), float64(params.minLooserChange)))

	return winnerChange, loserChange
}

func (s *service) triggerAsyncUpdates(ctx context.Context, game *models.Game, winner, loser *models.User, leaderboard []*models.LeaderBoardEntry) {
	timeLimit := gameutils.GetTimeLimitFromGameConfig(game.Config)

	go func() {
		if err := s.gameCache.DeleteGame(utils.DeriveContextWithoutCancel(ctx), game.ID.Hex()); err != nil {
			zlog.Error(ctx, "Failed to remove game from cache", err)
		}
	}()

	go func() {
		if err := rankingutils.UpdateRank(false, s.userRepo); err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()

	go func() {
		if err := rankingutils.UpdateRankingOnGamePlayed(utils.DeriveContextWithoutCancel(ctx), s.userRepo, []*models.User{winner, loser}); err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()

	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), winner.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()

	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), loser.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()

	go func() {
		activityType := gameutils.GetActivityTypeFromGameType(game.GameType)
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), loser.ID, activityType, *leaderboard[1].StatikCoinsEarned, int64(timeLimit*1000), game.ID); err != nil {
			zlog.Error(ctx, "Failed to update user activity of loser", err)
		}
	}()

	go func() {
		activityType := gameutils.GetActivityTypeFromGameType(game.GameType)
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), winner.ID, activityType, *leaderboard[0].StatikCoinsEarned, int64(timeLimit*1000), game.ID); err != nil {
			zlog.Error(ctx, "Failed to update user activity of winner", err)
		}
	}()

	if winner.IsBot == nil || (winner.IsBot != nil && !*winner.IsBot) {
		go func() {
			success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), game.Questions, winner.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to submit questions: ", err)
			}
			zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
		}()
	}

	if loser.IsBot == nil || (loser.IsBot != nil && !*loser.IsBot) {
		go func() {
			success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), game.Questions, loser.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to submit questions: ", err)
			}
			zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
		}()
	}
}
