package game

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/utils/systemErrors"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.uber.org/zap"
)

func (s *service) SubmitFlashAnzanAnswer(ctx context.Context, answerInput *models.SubmitFlashAnzanAnswerInput) (*models.Game, error) {
	zlog.Debug(ctx,
		"SubmitFlashAnzanAnswer",
		zap.String("gameID", answerInput.GameID.Hex()),
		zap.Int("maxScore", *answerInput.MaxScore),
		zap.Any("answer", answerInput),
	)

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		zlog.Error(ctx, "Failed to get user from context", err)
		return nil, fmt.Errorf("unauthorized: %w", err)
	}

	game, err := s.GetGameByID(ctx, answerInput.GameID)
	if err != nil {
		zlog.Error(ctx, "Failed to get game", err)
		return nil, fmt.Errorf("failed to get game: %w", err)
	}

	if game.GameStatus == models.GameStatusCreated {
		zlog.Warn(ctx, "Attempted to submit answer for a game that is created and hasn't started")
		return nil, systemErrors.ErrGameNotStarted
	}

	if game.GameStatus == models.GameStatusEnded {
		zlog.Warn(ctx, "Attempted to submit answer for an ended game")
		return nil, systemErrors.ErrGameAlreadyEnded
	}

	if game.GameStatus == models.GameStatusCancelled {
		zlog.Warn(ctx, "Attempted to submit answer for an Cancelled game")
		return nil, systemErrors.ErrGameCancelled
	}

	if game.GameStatus == models.GameStatusPaused {
		zlog.Info(ctx, "Resuming paused game")
		game.GameStatus = models.GameStatusStarted
	}

	if game.GameStatus != models.GameStatusStarted {
		zlog.Warn(ctx, "Attempted to submit answer for a game that hasn't started")
		return nil, systemErrors.ErrGameNotInStartedState
	}

	submissionTime := answerInput.TimeOfSubmission.Time()

	totalTimeOfLiveGame := int(submissionTime.Sub(*(game.StartTime)).Milliseconds())
	zlog.Debug(ctx, "Calculated total time of live game", zap.Int("totalTimeMs", totalTimeOfLiveGame))

	isCorrect := *answerInput.IsCorrect
	zlog.Debug(ctx, "Answer correctness", zap.Bool("isCorrect", isCorrect))

	timeLimit := gameutils.GetTimeLimitFromGameConfig(game.Config)

	if int(timeLimit*1000)+4000 <= totalTimeOfLiveGame {
		zlog.Info(ctx, "Game time limit reached, ending game")
		game.EndTime = &submissionTime
		return game, nil
	}

	points := -10
	if isCorrect {
		points = *answerInput.MaxScore
	} else {
		points = -10
	}

	gameutils.UpdateGameLeaderboard(game, userID, isCorrect, float64(points))

	zlog.Debug(ctx, "Calculated change in marks", zap.Float64("changeInMarks", float64(points)), zap.Bool("IS CORRECT ", isCorrect), zap.String("UserID", userID.Hex()))

	eventType := string(constants.GameEventEnum.INCORRECT_MOVE_MADE)
	if isCorrect {
		eventType = string(constants.GameEventEnum.CORRECT_MOVE_MADE)
	}

	err = s.publishSubmitAnswerGameEvent(ctx, game, constants.GameEventEnum.CORRECT_MOVE_MADE.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		zlog.Error(ctx, "Failed to update cached game", err)
	}

	zlog.Info(ctx, "Answer submission completed successfully",
		zap.Bool("isCorrect", isCorrect),
		zap.String("eventType", eventType))

	return game, nil
}
