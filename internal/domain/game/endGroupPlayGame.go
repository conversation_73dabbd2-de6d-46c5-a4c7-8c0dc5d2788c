package game

import (
	"context"
	"fmt"
	"math"
	"sort"

	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/game/utils/botutils"
	"matiksOfficial/matiks-server-go/internal/domain/userStreak"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils/rankingutils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

const CoinsEarnedInGroupPlay = 15

func (s *service) EndGroupPlayGame(ctx context.Context, gameID *primitive.ObjectID, game *models.Game) (*models.Game, error) {
	minValueForLooserRatingChange, maxValueForWinnerRatingChange := gameutils.GetMinMaxValueForRatingChange(game.GameType)

	gameStatus := game.GameStatus
	questions := game.Questions
	startTime := game.StartTime

	if gameStatus != models.GameStatusStarted {
		if gameStatus == models.GameStatusCancelled {
			game.GameStatus = models.GameStatusEnded
			return game, nil
		}

		zlog.Info(ctx, "Game is not in a started state, skipping end game")
		return nil, nil
	}

	game.GameStatus = models.GameStatusEnded

	err := s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	sortedLeaderboard := gameutils.UpdateLeaderboardRanks(game)

	if len(sortedLeaderboard) < 2 {
		return nil, fmt.Errorf("not enough players")
	}
	type PlayerGroup struct {
		players []*models.User
		score   int
	}

	playerGroups := make(map[int]*PlayerGroup)
	hasZeroScorePlayer := false

	for _, player := range sortedLeaderboard {
		if player.UserID == nil || player.TotalPoints == nil {
			continue
		}

		score := 0
		if player.TotalPoints != nil {
			score = int(*player.TotalPoints)
		}

		if score == 0 {
			hasZeroScorePlayer = true
		}

		if _, exists := playerGroups[score]; !exists {
			playerGroups[score] = &PlayerGroup{
				players: make([]*models.User, 0),
				score:   score,
			}
		}

		user, err := s.userService.GetUserByID(ctx, *player.UserID)
		if err != nil {
			return nil, fmt.Errorf("failed to get user %s: %w", *player.UserID, err)
		}

		if user == nil {
			return nil, fmt.Errorf("user %s not found", *player.UserID)
		}

		playerGroups[score].players = append(playerGroups[score].players, user)
	}

	var scores []int
	for score := range playerGroups {
		scores = append(scores, score)
	}
	sort.Sort(sort.Reverse(sort.IntSlice(scores)))

	maxScoreDifference := 0
	for _, questionObj := range questions {
		if questionObj.Question.Rating != nil && len(questionObj.Submissions) > 0 {
			maxScoreDifference += 4
		}
	}

	usersToUpdateRanking := make([]*models.User, 0)

	for i, score := range scores {
		group := playerGroups[score]
		isTie := false
		if i < len(scores)-1 && score == scores[i+1] {
			isTie = true
		}

		hasSomeoneNotSubmittedAnyQuestion := hasZeroScorePlayer
		bothNotSubmittedAnyQuestion := hasZeroScorePlayer && (score == 0)

		for _, player := range group.players {
			if player.Rating == nil {
				continue
			}

			expectedScore := 0.0
			totalOpponents := 0

			for otherScoreIdx, otherScore := range scores {
				if otherScore == score {
					continue
				}

				for _, opponent := range playerGroups[otherScore].players {
					if opponent.Rating == nil {
						continue
					}

					expectedWinProbability := 1 / (1 + math.Pow(10, (float64(*opponent.Rating)-float64(*player.Rating))/400))

					if i < otherScoreIdx {
						expectedScore += expectedWinProbability
					} else {
						expectedScore += 0
					}

					totalOpponents++
				}
			}

			KFactor := gameutils.GetKFactor(game.Config)

			var ratingChange int
			if !hasSomeoneNotSubmittedAnyQuestion {
				var scoreDifference float64
				if i == 0 && len(scores) > 1 {
					scoreDifference = float64(score - scores[1])
				} else if i > 0 {
					scoreDifference = float64(scores[0] - score)
				}

				normalizedScoreDifference := math.Min(math.Max(scoreDifference/math.Max(float64(maxScoreDifference), 1), 0), 1)
				adjustmentFactor := 0.2 + 1.6*normalizedScoreDifference

				if i == 0 && !isTie {
					actualScore := 1.0
					ratingChange = int(math.Max(1, KFactor*(actualScore-expectedScore/float64(totalOpponents))*adjustmentFactor))
				} else if isTie {
					actualScore := 0.5
					ratingChange = int(math.Max(1, KFactor*(actualScore-expectedScore/float64(totalOpponents))*adjustmentFactor))
					if i > 0 {
						ratingChange = int(math.Min(-1, KFactor*(actualScore-expectedScore/float64(totalOpponents))*adjustmentFactor))
					}
				} else {
					actualScore := 0.0
					ratingChange = int(math.Min(-1, KFactor*(actualScore-expectedScore/float64(totalOpponents))*adjustmentFactor))
				}
			} else {
				ratingChange = 0
				if bothNotSubmittedAnyQuestion {
					ratingChange = 0
				}
			}

			ratingChange = min(max(ratingChange, minValueForLooserRatingChange), maxValueForWinnerRatingChange)

			if game.GameType == models.GameTypeFastestFinger {
				ratingChange /= 2
				if i == 0 && !isTie {
					ratingChange = max(ratingChange, 1)
				} else {
					ratingChange = min(ratingChange, -1)
				}
			}

			newRating := *player.Rating + ratingChange
			if newRating < 100 {
				ratingChange = 100 - *player.Rating
			} else if newRating > 5000 {
				ratingChange = 5000 - *player.Rating
			}

			highestRating := int(math.Max(float64(*player.Rating), float64(*player.Rating+ratingChange)))
			if player.Stats != nil {
				highestRating = int(math.Max(float64(highestRating), float64(player.Stats.Hr)))
			}

			oldRating := *player.Rating

			playerGame := &models.UserGame{
				ID:       utils.AllocPtr(gameID.Hex()),
				ST:       startTime,
				IsWinner: utils.AllocPtr(i == 0 && !isTie),
			}

			initialBadge := player.Badge

			success, err := s.userGameBucketRepo.AddGame(ctx, player.ID, playerGame)
			if err != nil || !success {
				return nil, err
			}

			gameutils.UpdateUserStats(player, nil, highestRating, playerGame)
			oldRating = *player.Rating
			gameutils.GetUserWithNewRatingAfterApplyingRatingChange(player, ratingChange, game.GameType)
			newRating = *player.Rating
			gameutils.UpdateBadge(player, oldRating, newRating)

			err = botutils.UpdateHumanBotCache(player, s.cache, false)
			if err != nil {
				return nil, err
			}

			finalBadge := player.Badge

			err = s.userService.UpdateUserFromObject(ctx, player)
			if err != nil {
				return nil, fmt.Errorf("failed to update player: %w", err)
			}

			usersToUpdateRanking = append(usersToUpdateRanking, player)

			for j, entry := range sortedLeaderboard {
				if entry.UserID != nil && *entry.UserID == player.ID {
					coinsEarned := 0
					if sortedLeaderboard[j].TotalPoints != nil {
						coinsEarned = min(CoinsEarnedInGroupPlay, int(*sortedLeaderboard[j].TotalPoints))
					}
					sortedLeaderboard[j].RatingChange = utils.AllocPtr(ratingChange)
					sortedLeaderboard[j].StatikCoinsEarned = utils.AllocPtr(coinsEarned)
				}
			}

			if !gameutils.AreBadgesEqual(finalBadge, initialBadge) && !*player.IsGuest {
				go s.SendDelayedBadgeEvent(utils.DeriveContextWithoutCancel(ctx), player.ID, initialBadge, finalBadge)
			}

			if player.IsBot == nil || (player.IsBot != nil && !*player.IsBot) {
				go func(playerID primitive.ObjectID) {
					success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), questions, playerID)
					if err != nil {
						zlog.Error(ctx, "Failed to submit questions: ", err)
					}
					zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
				}(player.ID)
			}

			if err := s.globalLeaderboard.UpdateUserRating(ctx, &oldRating, *player.Rating); err != nil {
				zlog.Error(ctx, "Failed to update leaderboard", err, zap.Any("userID", player.ID.Hex()))
			}

			timeLimit := gameutils.GetTimeLimitFromGameConfig(game.Config)

			go func(playerID primitive.ObjectID, coinsEarned int) {
				activityType := gameutils.GetActivityTypeFromGameType(game.GameType)
				if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(
					utils.DeriveContextWithoutCancel(ctx),
					playerID,
					activityType,
					coinsEarned,
					int64(timeLimit)*1000,
					game.ID,
				); err != nil {
					zlog.Error(ctx, "Failed to update user activity", err)
				}
			}(player.ID, *sortedLeaderboard[i].StatikCoinsEarned)

			go func(playerID primitive.ObjectID) {
				if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), playerID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
					zlog.Error(ctx, "Failed to update streak", err)
				}
			}(player.ID)
		}
	}

	game.LeaderBoard = sortedLeaderboard
	game.GameStatus = models.GameStatusEnded
	game.RematchRequestedBy = nil

	if game.GameType != models.GameTypeFastestFinger && game.GameType != models.GameTypeFlashAnzan {
		gameutils.SaveMinifiedQuestions(game)
	}

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_ENDED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	go func() {
		if err := s.gameCache.DeleteGame(utils.DeriveContextWithoutCancel(ctx), gameID.Hex()); err != nil {
			zlog.Error(ctx, "Failed to remove game from cache", err)
		}
	}()

	go func() {
		if err := rankingutils.UpdateRank(false, s.userRepo); err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()

	go func() {
		if err := rankingutils.UpdateRankingOnGamePlayed(utils.DeriveContextWithoutCancel(ctx), s.userRepo, usersToUpdateRanking); err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()

	return game, nil
}
