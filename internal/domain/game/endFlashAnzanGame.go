package game

import (
	"context"
	"fmt"
	"math"

	"matiksOfficial/matiks-server-go/internal/constants"
	gameutils "matiksOfficial/matiks-server-go/internal/domain/game/utils"
	"matiksOfficial/matiks-server-go/internal/domain/userStreak"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/domain/utils/rankingutils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) EndFlashAnzanGame(ctx context.Context, gameID *primitive.ObjectID, game *models.Game) (*models.Game, error) {
	zlog.Debug(ctx, "Ending Flash Anzan Game ", zap.String("gameID", gameID.Hex()))

	minValueForLooserRatingChange, maxValueForWinnerRatingChange := gameutils.GetMinMaxValueForRatingChange(game.GameType)

	gameStatus := game.GameStatus
	questions := game.Questions
	startTime := game.StartTime

	if gameStatus != models.GameStatusStarted {
		if gameStatus == models.GameStatusCancelled {
			game.GameStatus = models.GameStatusEnded
			return game, nil
		}

		zlog.Info(ctx, "Game is not in a started state, skipping end game")
		return nil, nil
	}

	game.GameStatus = models.GameStatusEnded
	err := s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}
	sortedLeaderboard := gameutils.UpdateLeaderboardRanks(game)

	if len(sortedLeaderboard) < 2 {
		return nil, fmt.Errorf("not enough players")
	}

	hasSomeoneNotSubmittedAnyQuestion := *sortedLeaderboard[0].TotalPoints == 0 || *sortedLeaderboard[1].TotalPoints == 0
	isTie := *sortedLeaderboard[0].TotalPoints == *sortedLeaderboard[1].TotalPoints
	bothNotSubmittedAnyQuestion := hasSomeoneNotSubmittedAnyQuestion && isTie

	winnerID := sortedLeaderboard[0].UserID
	loserID := sortedLeaderboard[1].UserID

	if winnerID == nil || loserID == nil {
		return nil, fmt.Errorf("invalid player IDs")
	}

	winner, err := s.userService.GetUserByID(ctx, *winnerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get winner: %w", err)
	}

	loser, err := s.userService.GetUserByID(ctx, *loserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get loser: %w", err)
	}

	if winner == nil || loser == nil {
		return nil, fmt.Errorf("players not found")
	}

	winnerDefaultRating := gameutils.GetPlayerRatingByGameType(winner, models.GameTypeFlashAnzan)
	loserDefaultRating := gameutils.GetPlayerRatingByGameType(loser, models.GameTypeFlashAnzan)

	if winner.RatingV2 == nil {
		winner.RatingV2 = &models.UserRating{
			GlobalRating:     winner.Rating,
			FlashAnzanRating: utils.AllocPtr(winnerDefaultRating),
		}
	}

	if winner.RatingV2 != nil && winner.RatingV2.FlashAnzanRating == nil {
		winner.RatingV2.FlashAnzanRating = utils.AllocPtr(winnerDefaultRating)
	}

	if loser.RatingV2 == nil {
		loser.RatingV2 = &models.UserRating{
			GlobalRating:     loser.Rating,
			FlashAnzanRating: utils.AllocPtr(loserDefaultRating),
		}
	}

	if loser.RatingV2 != nil && loser.RatingV2.FlashAnzanRating == nil {
		loser.RatingV2.FlashAnzanRating = utils.AllocPtr(loserDefaultRating)
	}

	loserFARating := loser.RatingV2.FlashAnzanRating
	winnerFARating := winner.RatingV2.FlashAnzanRating

	expectedWinnerScore := 1 / (1 + math.Pow(10, (float64(*loserFARating)-float64(*winnerFARating))/400))
	expectedLoserScore := 1 / (1 + math.Pow(10, (float64(*winnerFARating)-float64(*loserFARating))/400))

	maxScoreDifference := 0
	for _, questionObj := range questions {
		if questionObj.Question.Rating != nil && len(questionObj.Submissions) > 0 {
			maxScoreDifference += 4
		}
	}

	scoreDifference := math.Abs(float64(*sortedLeaderboard[0].TotalPoints - *sortedLeaderboard[1].TotalPoints))
	normalizedScoreDifference := math.Min(math.Max(scoreDifference/math.Max(float64(maxScoreDifference), 1), 0), 1)
	adjustmentFactor := 0.2 + 1.6*normalizedScoreDifference
	KFactor := gameutils.GetKFactor(game.Config)

	var winnerRatingChange, loserRatingChange int
	if !hasSomeoneNotSubmittedAnyQuestion {
		if isTie {
			winnerRatingChange = int(math.Max(1, float64(KFactor)*(0.5-expectedWinnerScore)*adjustmentFactor))
			loserRatingChange = int(math.Min(-1, float64(KFactor)*(0.5-expectedLoserScore)*adjustmentFactor))
		} else {
			winnerRatingChange = int(math.Max(1, float64(KFactor)*(1-expectedWinnerScore)*adjustmentFactor))
			loserRatingChange = int(math.Min(-1, float64(KFactor)*(0-expectedLoserScore)*adjustmentFactor))
		}
	} else {
		winnerRatingChange = 0
		loserRatingChange = -2
		if bothNotSubmittedAnyQuestion {
			loserRatingChange = 0
		}
	}

	winnerRatingChange = int(math.Min(float64(winnerRatingChange), float64(maxValueForWinnerRatingChange)))
	loserRatingChange = int(math.Max(float64(loserRatingChange), float64(minValueForLooserRatingChange)))

	winnerHighestRating := int(math.Max(float64(*winnerFARating), float64(*winnerFARating+winnerRatingChange)))
	if winner.Stats != nil {
		winnerHighestRating = int(math.Max(float64(winnerHighestRating), float64(winner.Stats.Hr)))
	}

	loserHighestRating := int(math.Max(float64(*loserFARating), float64(*loserFARating+loserRatingChange)))
	if loser.Stats != nil {
		loserHighestRating = int(math.Max(float64(loserHighestRating), float64(loser.Stats.Hr)))
	}

	winnerGame := &models.UserGame{
		ID:         utils.AllocPtr(gameID.Hex()),
		ST:         startTime,
		IsWinner:   utils.AllocPtr(true),
		OpponentID: loserID,
	}

	loserGame := &models.UserGame{
		ID:         utils.AllocPtr(gameID.Hex()),
		ST:         startTime,
		IsWinner:   utils.AllocPtr(false),
		OpponentID: winnerID,
	}

	// winnerInitialBadge := winner.Badge
	// loserInitialBadge := loser.Badge

	success, err := s.userGameBucketRepo.AddGame(ctx, winner.ID, winnerGame)
	if err != nil || !success {
		return nil, err
	}
	success, err = s.userGameBucketRepo.AddGame(ctx, loser.ID, loserGame)
	if err != nil || !success {
		return nil, err
	}

	gameutils.UpdateUserStats(winner, loser, winnerHighestRating, winnerGame)
	gameutils.UpdateUserStats(loser, winner, loserHighestRating, loserGame)

	*winner.RatingV2.FlashAnzanRating += winnerRatingChange
	*loser.RatingV2.FlashAnzanRating += loserRatingChange

	err = s.userService.UpdateUserFromObject(ctx, winner)
	if err != nil {
		return nil, fmt.Errorf("failed to update winner: %w", err)
	}

	err = s.userService.UpdateUserFromObject(ctx, loser)
	if err != nil {
		return nil, fmt.Errorf("failed to update loser: %w", err)
	}

	sortedLeaderboard[0].RatingChange = utils.AllocPtr(winnerRatingChange)
	sortedLeaderboard[1].RatingChange = utils.AllocPtr(loserRatingChange)

	sortedLeaderboard[0].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForWinner(sortedLeaderboard[0])
	sortedLeaderboard[1].StatikCoinsEarned = gameutils.GetStatikCoinsEarnedForLoser(sortedLeaderboard[1])

	game.LeaderBoard = sortedLeaderboard
	game.GameStatus = models.GameStatusEnded

	err = s.publishGameEvent(ctx, game, constants.GameEventEnum.GAME_ENDED.String())
	if err != nil {
		zlog.Error(ctx, "Failed to publish game ended event", err)
	}

	if winner.IsBot == nil || (winner.IsBot != nil && !*winner.IsBot) {
		go func() {
			success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), game.Questions, winner.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to submit questions: ", err)
			}
			zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
		}()
	}

	if loser.IsBot == nil || (loser.IsBot != nil && !*loser.IsBot) {
		go func() {
			success, err := s.presetsService.SubmitUserQuestions(utils.DeriveContextWithoutCancel(ctx), game.Questions, loser.ID)
			if err != nil {
				zlog.Error(ctx, "Failed to submit questions: ", err)
			}
			zlog.Info(ctx, "Preset stats update status", zap.Bool("success", *success))
		}()
	}

	game.RematchRequestedBy = nil

	err = s.gameRepo.UpdateGame(ctx, game)
	if err != nil {
		return nil, err
	}

	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	timeLimit := gameutils.GetTimeLimitFromGameConfig(game.Config)

	go func() {
		if err := s.gameCache.DeleteGame(utils.DeriveContextWithoutCancel(ctx), gameID.Hex()); err != nil {
			zlog.Error(ctx, "Failed to remove game from cache", err)
		}
	}()

	go func() {
		if err := rankingutils.UpdateRankingOnGamePlayed(utils.DeriveContextWithoutCancel(ctx), s.userRepo, []*models.User{winner, loser}); err != nil {
			zlog.Error(ctx, "Failed to update ranking", err)
		}
	}()

	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), winner.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()
	go func() {
		if err := userStreak.UpdateUserStreak(utils.DeriveContextWithoutCancel(ctx), loser.ID, s.userRepo, true, s.notificationService, s.coreService, s.userStreakRepo); err != nil {
			zlog.Error(ctx, "Failed to update streak", err)
		}
	}()

	go func() {
		activityType := gameutils.GetActivityTypeFromGameType(game.GameType)
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), loser.ID, activityType, *sortedLeaderboard[1].StatikCoinsEarned, int64(timeLimit)*1000, game.ID); err != nil {
			zlog.Error(ctx, "Failed to update user activity of loser", err)
		}
	}()

	go func() {
		activityType := gameutils.GetActivityTypeFromGameType(game.GameType)
		if err := s.userService.UpdateUserStatikCoinsAndTimeSpent(utils.DeriveContextWithoutCancel(ctx), winner.ID, activityType, *sortedLeaderboard[0].StatikCoinsEarned, int64(timeLimit)*1000, game.ID); err != nil {
			zlog.Error(ctx, "Failed to update user activity of winner", err)
		}
	}()

	return game, nil
}
