package game

import (
	"context"
	"fmt"

	"matiksOfficial/matiks-server-go/internal/constants"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func (s *service) AcceptRematch(ctx context.Context, gameID primitive.ObjectID) (*models.Game, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, err
	}

	zlog.Debug(ctx, "AcceptRematch", zap.String("gameID", gameID.Hex()))

	game, err := s.GetGameByID(ctx, &gameID)
	if err != nil {
		return nil, fmt.Errorf("game not found %w", err)
	}

	if game.RematchRequestedBy == nil {
		return nil, fmt.Errorf("no rematch request for this game")
	}

	if *game.RematchRequestedBy == userID {
		return nil, fmt.Errorf("you cannot accept your own rematch request")
	}

	// Check if the previous game has a series ID
	var seriesID *primitive.ObjectID
	if game.SeriesID != nil {
		seriesID = game.SeriesID
	} else {
		// Create a new game series
		newSeries := &models.GameSeries{
			ID:        utils.AllocPtr(primitive.NewObjectID()),
			GameIds:   &[]primitive.ObjectID{gameID},
			PlayerIds: &[]primitive.ObjectID{userID, *game.RematchRequestedBy},
		}
		_, err = s.gameSeriesRepo.CreateGameSeries(ctx, newSeries)
		if err != nil {
			return nil, fmt.Errorf("failed to create game series: %w", err)
		}
		seriesID = newSeries.ID
	}

	opponentUserId := s.GetOpponentID(game.Players, userID)

	currentUser, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current user: %w", err)
	}
	opponentUser, err := s.userService.GetUserByID(ctx, opponentUserId)
	if err != nil {
		return nil, fmt.Errorf("failed to get opponent user: %w", err)
	}

	updatedUsers := []*models.User{currentUser, opponentUser}

	updatedPlayers := s.getPlayersFromUsers(updatedUsers, game.GameType)
	newGame, err := s.createGameWithPlayers(ctx, updatedPlayers, game.Config)
	if err != nil {
		return nil, err
	}

	// Update the game series document
	seriesUpdate := bson.M{
		"$addToSet": bson.M{
			"gameIds": newGame.ID,
		},
	}
	_, err = s.gameSeriesRepo.Collection().UpdateOne(ctx, bson.M{"_id": seriesID}, seriesUpdate)
	if err != nil {
		return nil, fmt.Errorf("failed to update game series: %w", err)
	}

	update := bson.M{
		"$unset": bson.M{
			"rematchRequestedBy": 1,
		},
		"$set": bson.M{"seriesId": seriesID},
	}

	newGameID := newGame.ID.Hex()

	rematchRequestedBy := *game.RematchRequestedBy

	game.RematchRequestedBy = nil

	filter := bson.M{"_id": gameID}
	err = s.gameRepo.UpdateOne(ctx, filter, update)
	if err != nil {
		zlog.Info(ctx, "Error deleting RematchRequestedBy field after auto-closing rematch request", zap.Error(err))

		return nil, err
	}
	err = s.gameCache.SetGame(ctx, game)
	if err != nil {
		return nil, err
	}

	// update game cache and set seriesId of game
	newGame.SeriesID = seriesID
	err = s.gameCache.SetGame(ctx, newGame)
	if err != nil {
		return nil, err
	}

	err = s.publishRematchEventToBothUsers(ctx, game, rematchRequestedBy, constants.RematchGameEnum.REMATCH_ACCEPTED.String(), &newGameID)
	if err != nil {
		return nil, err
	}

	return newGame, nil
}
