package friends

import (
	"context"
	"fmt"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *service) SendFriendRequest(ctx context.Context, sendRequestInput *models.FriendRequestInput) (bool, error) {
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return false, err
	}

	receiverId := sendRequestInput.UserID

	if receiverId == userID {
		return false, fmt.Errorf("you're trying to send friend request to yourself")
	}

	isExist, _, err := s.friendsRepo.CheckIfFriendRequestSent(ctx, userID, receiverId)
	if err != nil {
		return false, err
	}

	if isExist {
		return false, fmt.Errorf("friend Request already sent")
	}

	friendRequest := models.FriendRequest{
		SenderID:    userID,
		ReceiverID:  receiverId,
		Status:      utils.AllocPtr(models.FriendRequestStatusPending),
		SentAt:      utils.AllocPtr(time.Now()),
		RespondedAt: nil,
	}

	_, err = s.friendsRepo.CreateFriendRequest(ctx, friendRequest)
	if err != nil {
		return false, nil
	}

	user, err := s.userService.GetUserByID(ctx, userID)
	if err != nil {
		return false, err
	}
	receiverUser, err := s.userService.GetUserByID(ctx, receiverId)
	if err != nil {
		return false, err
	}
	notificationData := map[string]string{
		"type":     "FRIEND_REQUEST",
		"senderID": userID.Hex(),
		"username": user.Username,
		"url":      fmt.Sprintf("https://www.matiks.com/profile/%s/friends", receiverUser.Username),
	}

	err = s.notificationService.SendPushNotification(
		ctx,
		"You have a new friend request !",
		"New Friend Request",
		notificationData,
		[]primitive.ObjectID{receiverId},
	)
	if err != nil {
		zlog.Error(ctx, "error sending push notification", err)
	}

	followReq := models.FollowUserInput{
		UserID: receiverId,
	}

	_, err = s.FollowUser(ctx, &followReq)
	if err != nil {
		return true, nil
	}

	return true, nil
}
