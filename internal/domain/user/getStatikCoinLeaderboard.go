package user

import (
	"context"
	"time"

	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func (s *service) GetStatikCoinsLeaderboard(ctx context.Context, page int, pageSize *int, leaderboardType *models.StatikCoinLeaderboardType) (*models.StatikCoinLeaderboardPage, error) {
	if page == 0 {
		defaultPageNumber := 1
		page = defaultPageNumber
	}

	if pageSize == nil {
		defaultPageSize := DefaultPageSize
		pageSize = &defaultPageSize
	}

	skip := (page - 1) * *pageSize

	startDate := time.Now().UTC()
	switch *leaderboardType {
	case models.StatikCoinLeaderboardTypeDaily:
		startDate = time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, time.UTC)
	case models.StatikCoinLeaderboardTypeWeekly:
		startDate = startDate.AddDate(0, 0, -int(startDate.Weekday()))
		startDate = time.Date(startDate.Year(), startDate.Month(), startDate.Day(), 0, 0, 0, 0, time.UTC)
	case models.StatikCoinLeaderboardTypeMonthly:
		startDate = time.Date(startDate.Year(), startDate.Month(), 1, 0, 0, 0, 0, time.UTC)
	case models.StatikCoinLeaderboardTypeAllTime:
		startDate = time.Time{}
	}

	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{
			"date": bson.M{"$gte": startDate},
		}}},
		{{Key: "$group", Value: bson.M{
			"_id":         "$userId",
			"statikCoins": bson.M{"$sum": "$statikCoinsEarned"},
		}}},
		{{Key: "$sort", Value: bson.M{"statikCoins": -1}}},
		{{Key: "$facet", Value: bson.M{
			"metadata": bson.A{bson.M{"$count": "total"}},
			"data": bson.A{
				bson.M{"$skip": skip},
				bson.M{"$limit": *pageSize},
				bson.M{"$lookup": bson.M{
					"from":         "users",
					"localField":   "_id",
					"foreignField": "_id",
					"as":           "user",
					"pipeline": bson.A{
						bson.M{"$project": bson.M{
							"_id":             1,
							"name":            1,
							"username":        1,
							"profileImageUrl": 1,
							"rating":          1,
							"badge":           1,
							"globalRank":      1,
						}},
					},
				}},
				bson.M{"$unwind": "$user"},
			},
		}}},
		{{Key: "$project", Value: bson.M{
			"results": bson.M{
				"$map": bson.M{
					"input": "$data",
					"as":    "result",
					"in": bson.M{
						"user":        "$$result.user",
						"statikCoins": "$$result.statikCoins",
						"rank":        bson.M{"$add": bson.A{skip, bson.M{"$indexOfArray": bson.A{"$data", "$$result"}}, 1}},
					},
				},
			},
			"pageNumber":   bson.M{"$literal": page},
			"pageSize":     bson.M{"$literal": *pageSize},
			"totalResults": bson.M{"$arrayElemAt": bson.A{"$metadata.total", 0}},
		}}},
	}

	cur, err := s.userActivityRepo.AggregateProjected(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var results []bson.M
	if err := cur.All(ctx, &results); err != nil {
		return nil, err
	}

	if len(results) == 0 || results[0]["results"] == nil {
		return &models.StatikCoinLeaderboardPage{
			Results:      []*models.StatikCoinLeaderboardEntry{},
			PageNumber:   page,
			PageSize:     *pageSize,
			HasMore:      utils.AllocPtr(false),
			TotalResults: utils.AllocPtr(0),
		}, nil
	}

	resultData := results[0]
	totalResults := utils.ToInt(resultData["totalResults"])
	hasMore := totalResults > (skip + *pageSize)

	leaderboardEntries := make([]*models.StatikCoinLeaderboardEntry, 0)
	if resultsArr, ok := resultData["results"].(bson.A); ok {
		for _, result := range resultsArr {
			if resultDoc, ok := result.(bson.M); ok {
				if user, ok := resultDoc["user"].(bson.M); ok {
					leaderboardEntries = append(leaderboardEntries, &models.StatikCoinLeaderboardEntry{
						User:        utils.ExtractUserPublicDetails(user),
						StatikCoins: utils.AllocPtr(utils.ToInt(resultDoc["statikCoins"])),
						Rank:        utils.AllocPtr(utils.ToInt(resultDoc["rank"])),
					})
				}
			}
		}
	}

	return &models.StatikCoinLeaderboardPage{
		Results:      leaderboardEntries,
		PageNumber:   page,
		PageSize:     *pageSize,
		HasMore:      utils.AllocPtr(hasMore),
		TotalResults: utils.AllocPtr(totalResults),
	}, nil
}
