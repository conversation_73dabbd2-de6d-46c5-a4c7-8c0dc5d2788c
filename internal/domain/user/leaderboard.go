package user

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	"matiksOfficial/matiks-server-go/internal/constants"
	"matiksOfficial/matiks-server-go/internal/domain/utils"
	"matiksOfficial/matiks-server-go/internal/models"
	zlog "matiksOfficial/matiks-server-go/pkg/logger"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	DefaultPageSize       = 20
	DefaultTopPlayersSize = 3
)

func (s *service) Leaderboard(ctx context.Context, countryCode, searchKey *string, first *int, after *string) (*models.LeaderboardConnection, error) {
	zlog.Info(ctx, "Getting leaderboard")

	limit := DefaultPageSize
	if first != nil {
		limit = *first
	}

	query := bson.M{"isGuest": false}
	sortField := "globalRank"

	if countryCode != nil {
		query["countryCode"] = *countryCode
		query["countryRank"] = bson.M{"$exists": true}
		sortField = "countryRank"
	} else {
		query["globalRank"] = bson.M{"$exists": true}
	}

	if searchKey != nil {
		query["name"] = bson.M{"$regex": *searchKey, "$options": "i"}
	}

	findOptions := options.Find().
		SetSort(bson.M{sortField: 1}).
		SetLimit(int64(limit + 1)) // Fetch one extra to determine if there's a next page

	if after != nil {
		afterID, err := primitive.ObjectIDFromHex(*after)
		if err != nil {
			return nil, err
		}
		query["_id"] = bson.M{"$gt": afterID}
	}

	users, err := s.userRepo.Find(ctx, query, findOptions)
	if err != nil {
		return nil, err
	}

	hasNextPage := len(users) > limit
	if hasNextPage {
		users = users[:limit] // Remove the extra user
	}

	edges := make([]*models.LeaderboardEdge, len(users))
	for i, user := range users {
		edges[i] = &models.LeaderboardEdge{
			Node:   utils.GetUserPublicDetails(user),
			Cursor: user.ID.Hex(),
		}
	}

	var endCursor *string
	if len(edges) > 0 {
		lastCursor := edges[len(edges)-1].Cursor
		endCursor = &lastCursor
	}

	pageInfo := &models.PageInfo{
		HasNextPage: hasNextPage,
		EndCursor:   endCursor,
	}

	return &models.LeaderboardConnection{
		Edges:    edges,
		PageInfo: pageInfo,
	}, nil
}

func (s *service) LeaderboardNew(ctx context.Context, countryCode, searchKey *string, page, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error) {
	zlog.Info(ctx, "Getting leaderboardV2")
	var err error
	currentPage := 1
	if page != nil {
		currentPage = *page
	}

	limit := DefaultPageSize
	if pageSize != nil {
		limit = *pageSize
	}

	var users []*models.User
	if ratingType == nil {
		users, err = s.userRepo.GetPaginatedUsersByGlobalRank(ctx, int64(currentPage), int64(limit))
		if err != nil {
			return nil, fmt.Errorf("failed to fetch users: %w", err)
		}
	} else {
		users, err = s.userRepo.GetPaginatedUsersByRatingType(ctx, int64(currentPage), int64(limit), *ratingType)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch users: %w", err)
		}
	}

	if len(users) == 0 || users[0] == nil {
		return nil, fmt.Errorf("users not found")
	}

	totalCount, err := s.userRepo.GetTotalUsersCount(ctx, false, ratingType)
	if err != nil {
		return nil, err
	}
	leaderboardEdges := make([]*models.LeaderboardEdge, 0, len(users))
	for _, user := range users {
		if user == nil {
			return nil, fmt.Errorf("user found nil")
		}
		leaderboardEdges = append(leaderboardEdges, &models.LeaderboardEdge{
			Cursor: user.ID.Hex(),
			Node:   utils.GetUserPublicDetails(user),
		})
	}
	leaderboardConnection := &models.UserLeaderboardPage{
		TotalCount: int(totalCount),
		Edges:      leaderboardEdges,
	}
	return leaderboardConnection, nil
}

func (s *service) LeaderBoardV3(ctx context.Context, countryCode, searchKey *string, page, pageSize *int) (*models.UserLeaderboardPage, error) {
	zlog.Info(ctx, "Getting leaderboardV3")
	var err error
	currentPage := 1
	if page != nil {
		currentPage = *page
	}

	limit := DefaultPageSize
	if pageSize != nil {
		limit = *pageSize
	}
	leaderboard, err := s.globalLeaderboard.GetLeaderboard(ctx, models.LeaderboardParams{
		Page:     currentPage,
		PageSize: limit,
	})
	if err != nil {
		return nil, err
	}
	return leaderboard, nil
}

func (s *service) GetGlobalTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error) {
	zlog.Info(ctx, "Getting Global Top Players ")

	topPlayers, err := s.userRepo.GetGlobalTopPlayers(ctx, DefaultTopPlayersSize)
	if err != nil {
		zlog.Error(ctx, "Error getting global top players", err)
		return nil, err
	}
	return topPlayers, nil
}

func (s *service) GetFriendsLeaderboard(ctx context.Context, page, pageSize *int, ratingType *string) (*models.UserLeaderboardPage, error) {
	zlog.Info(ctx, "Getting friends leaderboard for rating type", zap.String("ratingType", *ratingType))
	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}
	switch *ratingType {
	case constants.GlobalRating, constants.MemoryRating, constants.AbilityRating:
	default:
		return nil, fmt.Errorf("invalid rating type: %s", *ratingType)
	}

	currentPage := 1
	if page != nil {
		currentPage = *page
	}

	currentPageSize := DefaultPageSize
	if pageSize != nil {
		currentPageSize = *pageSize
	}

	leaderBoard, err := s.userRepo.GetFriendsLeaderboardByRatingType(ctx, userID, int64(currentPage), int64(currentPageSize), *ratingType)
	if err != nil {
		zlog.Error(ctx, "Error getting friends leaderboard", err)
		return nil, err
	}

	return leaderBoard, nil
}

func (s *service) GetFriendsTopPlayers(ctx context.Context) (*models.TopPlayersLeaderboard, error) {
	zlog.Info(ctx, "Getting Friends Top Players")

	userID, err := utils.GetUserFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("unauthorized: user not authenticated")
	}
	friendsTopPlayers, err := s.userRepo.GetFriendsTopPlayers(ctx, userID, DefaultTopPlayersSize)
	if err != nil {
		zlog.Error(ctx, "Error getting friends top players", err)
		return nil, err
	}
	return friendsTopPlayers, nil
}
