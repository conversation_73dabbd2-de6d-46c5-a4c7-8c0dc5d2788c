package questionsGenerator

type DigitDifficultyLevel string

var DigitDifficultyLevels = struct {
	EASY   DigitDifficultyLevel
	MEDIUM DigitDifficultyLevel
	HARD   DigitDifficultyLevel
}{
	EASY:   "EASY",
	MEDIUM: "MEDIUM",
	HARD:   "HARD",
}

const (
	QUESTION_TYPE_FILL_IN_THE_BLANKS = "fill_in_the_blanks"
	QUESTION_TAG_ADDITION            = "addition"
	QUESTION_TAG_ARITHMETIC          = "arithmetic"
	QUESTION_TAG_MULTIPLICATION      = "MULTIPLICATION"
	QUESTION_TAG_DIVISION            = "division"
	QUESTION_TAG_MOD                 = "MOD"
	QUESTION_TAG_ROOT                = "ROOT"
	QUESTION_TAG_EXPONENT            = "EXPONENT"
	QUESTION_TAG_HCF                 = "HFC"
	QUESTION_TAG_LCM                 = "LCM"
	QUESTION_TAG_PRIME_FACTORIZATION = "PRIME_FACTORIZATION"
	QUESTION_TAG_MULTIPLE_OPERATORS  = "MULTIPLE_OPERATORS"
	QUESTION_TAG_SUM_OF_SQUARES      = "SUM_OF_SQUARES"
)

type NumConfig struct {
	NumDigit int
	Level    DigitDifficultyLevel
}

type MultiOpNumConfig struct {
	NumConfig []NumConfig
	Operators int
	Brackets  int
}
