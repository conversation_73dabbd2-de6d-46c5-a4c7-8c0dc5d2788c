package presets

import (
	"fmt"
	"sort"
	"strconv"
	"strings"

	"matiksOfficial/matiks-server-go/internal/models"
)

func PresetIdentifierGenerator(presetCategory models.PresetCategory, numbers []int) string {
	digitCounts := make([]int, 0, len(numbers))
	for _, num := range numbers {
		numDigits := len(strconv.Itoa(num))
		digitCounts = append(digitCounts, numDigits)
	}

	sort.Sort(sort.Reverse(sort.IntSlice(digitCounts)))
	return presetCategory.String() + "_" + strings.Trim(strings.Join(strings.Fields(fmt.Sprint(digitCounts)), ","), "[]")
}
