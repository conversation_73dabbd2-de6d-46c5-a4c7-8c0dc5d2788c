package questions

import (
	"fmt"
	"math"
	"math/rand/v2"
	"strconv"
	"strings"
	"time"

	"matiksOfficial/matiks-server-go/internal/models"
)

type QuestionInfo struct {
	Type      models.PresetCategory
	DigitList []int
}

func ParseIdentifier(identifier string) (QuestionInfo, error) {
	parts := strings.Split(identifier, "_")
	if len(parts) != 2 {
		return QuestionInfo{}, fmt.Errorf("invalid identifier format: %s", identifier)
	}

	questionTypeStr := parts[0]
	var questionType models.PresetCategory
	switch questionTypeStr {
	case "ADD":
		questionType = models.PresetCategoryAdd
	case "ADDSUB":
		questionType = models.PresetCategoryAddsub
	case "MULT":
		questionType = models.PresetCategoryMult
	case "DIV":
		questionType = models.PresetCategoryDiv
	case "MOD":
		questionType = models.PresetCategoryMod
	case "ROOT":
		questionType = models.PresetCategoryRoot
	case "EXPO":
		questionType = models.PresetCategoryExpo
	case "HCF":
		questionType = models.PresetCategoryHcf
	case "LCM":
		questionType = models.PresetCategoryLcm
	case "PF":
		questionType = models.PresetCategoryPf
	case "MULOP":
		questionType = models.PresetCategoryMulOp
	case "SOS":
		questionType = models.PresetCategorySOS
	default:
		return QuestionInfo{}, fmt.Errorf("invalid question type: %s", questionTypeStr)
	}

	digitListStr := strings.Split(parts[1], ",")
	digitList := make([]int, len(digitListStr))

	for i, digitStr := range digitListStr {
		digit, err := strconv.Atoi(digitStr)
		if err != nil {
			return QuestionInfo{}, fmt.Errorf("invalid digit count: %s", digitStr)
		}
		digitList[i] = digit
	}

	return QuestionInfo{
		Type:      questionType,
		DigitList: digitList,
	}, nil
}

func calculateExponentialDifficulty(digits int) float64 {
	baseDifficulty := 10.0
	growthFactor := 1.75

	return baseDifficulty * math.Pow(growthFactor, float64(digits))
}

func CalculateDifficulty(question QuestionInfo) float64 {
	// Adjusted type difficulty based on test patterns
	typeDifficulty := map[models.PresetCategory]float64{
		models.PresetCategoryAdd:    1.0,
		models.PresetCategoryAddsub: 1.5,
		models.PresetCategoryMult:   5.0,
		models.PresetCategoryDiv:    1.8,
		models.PresetCategoryMod:    3.0,
		models.PresetCategoryRoot:   12.0,
		models.PresetCategoryExpo:   10.0,
		models.PresetCategoryHcf:    3.0,
		models.PresetCategoryLcm:    3.0,
		models.PresetCategoryPf:     5.0,
		models.PresetCategoryMulOp:  3.5,
		models.PresetCategorySOS:    5.0,
	}

	if question.Type == models.PresetCategoryPf || question.Type == models.PresetCategorySOS {
		// For PF, we only consider the first number in digitList
		// as it represents the number to be factorized
		if len(question.DigitList) > 0 {
			return calculateExponentialDifficulty(question.DigitList[0])
		}
		return typeDifficulty[question.Type] // fallback to base difficulty
	}

	// Sum up digits with weights
	digitComplexity := 0.0
	for _, digits := range question.DigitList {
		// Larger digit numbers grow faster in complexity
		digitComplexity += math.Pow(float64(digits), 1.2)
	}

	// Operand count has a significant impact
	operandCountFactor := math.Pow(float64(len(question.DigitList)), 0.8)

	// Combine type difficulty with operand count
	typeFactor := typeDifficulty[question.Type] * operandCountFactor

	// Multiply the factors together
	return digitComplexity * typeFactor
}

// EstimateTimeToSolve in ms
func EstimateTimeToSolve(rating int, identifier string) (float64, error) {
	if rating < 100 || rating > 5000 {
		return 0, fmt.Errorf("rating must be between 100 and 5000")
	}

	question, err := ParseIdentifier(identifier)
	if err != nil {
		return 0, err
	}

	difficulty := CalculateDifficulty(question)

	// Base multiplier for all operations
	baseTime := 1000.0

	// Calculate rating factor with a non-linear relationship
	// The exponent 1.5 makes the curve steeper for low ratings
	ratingFactor := math.Pow(500.0/float64(rating), 1.5)

	// For high-difficulty questions, rating matters more
	if difficulty > 20 {
		// Increase the effect of rating for very difficult problems
		highDifficultyAdjustment := math.Log10(difficulty) * 0.2
		ratingFactor = math.Pow(500.0/float64(rating), 1.5+highDifficultyAdjustment)
	}

	if rating > 3500 {
		ratingFactor = math.Pow(500.0/float64(rating), 2.3)
	}

	// Calculate time to solve
	timeToSolve := baseTime * difficulty * ratingFactor

	// Adjust based on operation type and number of operands
	if question.Type == models.PresetCategoryMult && len(question.DigitList) > 1 {
		// For multiplication with multi-digit numbers, add extra time
		digitSum := 0
		for _, d := range question.DigitList {
			digitSum += d
		}
		// Add a scaling factor for very large multiplications
		if digitSum > 3 {
			timeToSolve *= math.Log2(1.0 + (float64(digitSum - 3)))
		}
	}

	// Ensure minimum solve time
	minTime := 500.0
	if timeToSolve < minTime {
		timeToSolve = minTime
	}

	return timeToSolve, nil
}

func updatedTop10MathletesArray(timeTaken int, top10Mathletes []*int) []*int {
	panic("NOT IMPLEMENTED FUNCTION")
}

// in ms
func getFallbackMeanTime(userRating, questionRating int) float64 {
	tBase := 5000.0
	kU := 1.0
	kQ := 1.1

	if userRating > 3750 {
		kU = 1.5
	} else if userRating > 3500 {
		kU = 1.4
	} else if userRating > 3000 {
		kU = 1.2
	} else if userRating > 2500 {
		kU = 1.1
	}

	return tBase * ((kQ * float64(questionRating)) / (kU * float64(userRating)))
}

func getPresetMeanTime(presetIdentifier string, questionRating, userRating int) float64 {
	estimatedTime, err := EstimateTimeToSolve(userRating, presetIdentifier)
	if err != nil {
		return getFallbackMeanTime(userRating, questionRating)
	}

	return estimatedTime
}

func GetExpectedTimeToSolveQuestion(userRating, questionRating int, presetIdentifier string) time.Duration {
	meanTime := getPresetMeanTime(presetIdentifier, questionRating, userRating)
	// meanTime := getFallbackMeanTime(userRating, questionRating)
	expectedTime := addGaussianNoise(meanTime, 500, 0.0, meanTime/3.5)

	return time.Duration(expectedTime * float64(time.Millisecond))
}

func addGaussianNoise(meanTime, minTime, meanNoise, stdDev float64) float64 {
	u1 := rand.Float64()
	u2 := rand.Float64()
	gaussianNoise := meanNoise + stdDev*math.Sqrt(-2*math.Log(u1))*math.Cos(2*math.Pi*u2)

	noisyTime := meanTime + gaussianNoise

	if noisyTime < minTime {
		noisyTime = minTime
	}

	return noisyTime
}

func GetBotFlashAnzanMaxScore(botRating int) int {
	rating := botRating

	ratingThresholds := []int{800, 1000, 1200, 1400, 1600, 1800, 2000, 2200, 2400, 2600, 2800, 3000, 3200, 3400, 3600, 3800, 4000}
	maxScores := []int{90, 90, 90, 150, 170, 200, 240, 260, 300, 340, 380, 440, 500, 550, 640, 700, 750}

	maxScore := 750

	for i, threshold := range ratingThresholds {
		if rating <= threshold {
			maxScore = maxScores[i]
			break
		}
	}

	return maxScore
}

func ShouldBotAnswerCorrectly(maxScore int) bool {
	score := maxScore

	minScoreValue := 90
	maxScoreValue := 750
	maxProb := 0.95
	minProb := 0.2

	if score < minScoreValue {
		score = minScoreValue
	} else if score > maxScoreValue {
		score = maxScoreValue
	}

	// Linear interpolation between probability values
	probability := maxProb - (float64(score-minScoreValue)/float64(maxScoreValue-minScoreValue))*(maxProb-minProb)

	// Randomly determining if the answer should be correct based on calculated probability
	return rand.Float64() < probability
}
