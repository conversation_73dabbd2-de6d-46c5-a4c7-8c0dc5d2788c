"""
Represents different types of announcements the system can display.
"""
enum AnnouncementType {
  GENERAL # General information or updates
  FEATURE_UPDATE # Announcing new or updated features
  PROMOTION # Special offers, discounts, etc.
  EVENT # Information about upcoming events or contests
  MAINTENANCE # Planned maintenance notifications
  SURVEY # Requesting user feedback via a survey
}

"""
Defines the action triggered by a Call-to-Action button.
"""
enum CTAActionType {
  OPEN_URL # Opens the provided URL in a browser/webview
  NAVIGATE_INTERNAL # Triggers navigation within the application
  DISMISS # Simply dismisses the announcement
}

"""
Represents a Call-to-Action (CTA) element within an announcement.
"""
type CallToAction {
  "The text displayed on the button."
  text: String!
  "The URL or internal route associated with the action (required for OPEN_URL, NAVIGATE_INTERNAL)."
  target: String
  "The type of action this CTA performs."
  actionType: CTAActionType!
  "Optional style hint for the button (e.g., 'primary', 'secondary'). Frontend interprets this."
  style: String
}

"""
Represents a system announcement intended for users.
"""
type Announcement {
  "Unique identifier for the announcement."
  id: ID!
  "The category or type of the announcement."
  type: AnnouncementType!
  "The main title or headline of the announcement (optional)."
  title: String
  description: String!
  "Rive animation url"
  riveAnimationUrl: String
  "URL for an optional image associated with the announcement."
  imageUrl: String
  "URL for optional video or audio content."
  mediaUrl: String
  "List of Call-to-Action buttons for this announcement."
  ctas: [CallToAction!]
  "Priority for ordering announcements (higher value means higher priority). Defaults to 0."
  priority: Int
  "Timestamp when the announcement was created in the system."
  createdAt: Time!
  "Timestamp when the announcement should become visible to users (optional, defaults to createdAt)."
  publishedAt: Time
  "Timestamp when the announcement should expire and no longer be shown (optional)."
  expiresAt: Time
}

"""
Input for creating a CallToAction.
"""
input CTAInput {
  "The text displayed on the button."
  text: String!
  "The URL or internal route associated with the action (required for OPEN_URL, NAVIGATE_INTERNAL)."
  target: String
  "The type of action this CTA performs."
  actionType: CTAActionType!
  "Optional style hint for the button (e.g., 'primary', 'secondary'). Frontend interprets this."
  style: String
}

"""
Input for creating a new announcement.
"""
input CreateAnnouncementInput {
  type: AnnouncementType!
  title: String
  description: String!
  riveAnimationUrl: String
  imageUrl: String
  mediaUrl: String
  ctas: [CTAInput!]
  priority: Int
  publishedAt: Time
  expiresAt: Time
}

"""
Input for updating an existing announcement. Fields are optional.
"""
input UpdateAnnouncementInput {
  type: AnnouncementType
  title: String
  description: String
  imageUrl: String
  mediaUrl: String
  ctas: [CTAInput!]
  priority: Int
  publishedAt: Time
  expiresAt: Time
  riveAnimationUrl: String
}

"""
Standard response for mutations that primarily indicate success/failure.
"""
type AnnouncementMutationResponse {
  success: Boolean!
  message: String
}

# --- Queries ---

extend type Query {
  """
  Fetches unread announcements for the currently authenticated user, respecting
  publication dates, expiry dates, and targeting. Ordered by priority then creation date.
  """
  getUnreadAnnouncements(limit: Int = 10, offset: Int = 0): [Announcement!]!
    @auth

  """
  Fetches a specific announcement by its ID.
  """
  getAnnouncement(id: ID!): Announcement @auth

  """
  Fetches all announcements (primarily for admin interfaces). Supports filtering and pagination.
  Requires appropriate permissions.
  """
  getAllAnnouncements(
    limit: Int = 50
    offset: Int = 0
    type: AnnouncementType
  ): [Announcement!]! @auth
}

extend type Mutation {
  """
  Marks a specific announcement as read for the current user.
  """
  markAnnouncementAsRead(announcementId: ID!): AnnouncementMutationResponse!
    @auth

  """
  Marks all currently unread announcements as read for the current user.
  """
  markAllAnnouncementsAsRead: AnnouncementMutationResponse! @auth

  # --- Admin Mutations (Require appropriate permissions) ---

  """
  Creates a new announcement. Requires admin privileges.
  """
  createAnnouncement(input: CreateAnnouncementInput!): Announcement! @auth

  """
  Updates an existing announcement. Requires admin privileges.
  """
  updateAnnouncement(id: ID!, input: UpdateAnnouncementInput!): Announcement!
    @auth

  """
  Deletes an announcement. Requires admin privileges.
  """
  deleteAnnouncement(id: ID!): AnnouncementMutationResponse! @auth
}
