type PlatformStats {
  totalUsers: Int!
  totalGames: Int!
  totalSignedInUsers: Int!
}

enum Visibility {
  PUBLIC
  PRIVATE
}

enum SortOrder {
  ASC
  DESC
}

input SortOptions {
  sortBy: String!
  sortDirection: Int!
}

extend type Query {
  getPlatformStats: PlatformStats!
}

type File {
  name: String!
  content: String!
  contentType: String!
  url: String!
}

extend type Mutation {
  uploadFiles(files: [Upload!]!): [File!]! @auth
  sendOTP(email: String!): Boolean! @auth
  verifyOTP(otp: String!): Boolean! @auth
}
