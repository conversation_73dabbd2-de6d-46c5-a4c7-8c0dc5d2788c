input Feedback {
  email: String!
  phone: String!
  message: String!
}

extend type Mutation {
  registerDeviceToken(
    pushNotificationToken: String!
    deviceId: String
    platform: String
  ): DeviceTokenRegistrationResponse @auth
  unregisterDeviceToken(
    pushNotificationToken: String
    deviceId: String
  ): DeviceTokenRegistrationResponse @auth
  sendFeedback(input: Feedback!): <PERSON><PERSON><PERSON> @auth
}
