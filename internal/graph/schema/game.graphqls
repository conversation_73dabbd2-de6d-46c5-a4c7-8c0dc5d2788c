type Game {
    _id: ID!
    createdBy: ID!

    gameCategory: GAME_CATEGORY
    gameMode: GAME_MODE
    gameType: GAME_TYPE!
    gameStatus: GAME_STATUS!
    players: [Player!]!
    rematchRequestedBy: ID

    config: GameConfig @deprecated
    questions: [GameQuestion] @deprecated
    showdownGameConfig: ShowdownGameConfig @deprecated

    minifiedQuestions: [String]
    encryptedQuestions: [String]
    leaderBoard: [LeaderBoardEntry]
    startTime: Time
    endTime: Time
    seriesId: ID
    showdownId: ID
}

type GameTypeSpecificConfig {
    type: GAME_TYPE!
}

type GameCategorySpecificConfig {
    category: GAME_CATEGORY!
    blitz: DefaultGameConfig
    classical: DefaultGameConfig
    memory: DefaultGameConfig
    puzzle: PuzzleGameConfig
}

type GroupPlayGameConfig {
    maxTimePerQuestion: Int
    difficultyLevel: [Int!]
    maxGapBwGame: Int
    maxPlayers: Int
    minPlayers: Int
    numPlayers: Int
    questionTags: [String]
}

type DefaultGameConfig {
    timeLimit: Int
}

type DefaultGameModeConfig {
    numPlayers: Int
}

type GameModeSpecificConfig {
    mode: GAME_MODE!
    sumdayShowdown: ShowdownGameConfig
    onlineSearch: DefaultGameModeConfig
    onlineChallenge: DefaultGameModeConfig
    practice: DefaultGameModeConfig
    rushWithTime: DefaultGameModeConfig
    rushWithoutTime: DefaultGameModeConfig
    groupPlay: GroupPlayGameConfig
    playViaLink: DefaultGameModeConfig
    survivalSaturday: DefaultGameModeConfig
}

enum GAME_MODE {
    ONLINE_SEARCH
    ONLINE_CHALLENGE
    PRACTICE
    RUSH_WITH_TIME
    RUSH_WITHOUT_TIME
    GROUP_PLAY
    PLAY_VIA_LINK
    SUMDAY_SHOWDOWN
    SURVIVAL_SATURDAY
}

enum GAME_CATEGORY {
    BLITZ
    CLASSICAL
    MEMORY
    PUZZLE
}

type Player {
    userId: ID!
    rating: Int
    statikCoins: Int
    status: PLAYER_STATUS
    timeLeft: Int
}

type GameQuestion {
    question: Question
    submissions: [Submission]
    stats: GameQuestionStats
}

type GameQuestionStats {
    fastestTime: Int
    userIds: [ID]
}

type Submission {
    userId: ID
    timeTaken: Int
    points: Int
    submissionTime: Date
    isCorrect: Boolean
    inCorrectAttempts: Int
    submittedValues: [String]
}

type LeaderBoardEntry {
    userId: ID
    correct: Int
    incorrect: Int
    totalPoints: Float
    ratingChange: Int
    statikCoinsEarned: Int
    rank: Int
}

type GameConfig {
    "timeLimit in seconds"
    timeLimit: Int @deprecated
    numPlayers: Int @deprecated
    gameType: GAME_TYPE @deprecated
    questionTags: [String] @deprecated
    difficultyLevel: [Int!] @deprecated
    maxTimePerQuestion: Int @deprecated

    categorySpecificConfig: GameCategorySpecificConfig
    gameTypeSpecificConfig: GameTypeSpecificConfig
    modeSpecificConfig: GameModeSpecificConfig
}

input GameConfigInput {
    timeLimit: Int @deprecated
    numPlayers: Int @deprecated
    gameType: GAME_TYPE @deprecated
    questionTags: [String!] @deprecated
    difficultyLevel: [Int!] @deprecated
    maxTimePerQuestion: Int @deprecated

    categorySpecificConfig: GameCategorySpecificConfigInput
    gameTypeSpecificConfig: GameTypeSpecificConfigInput
    modeSpecificConfig: GameModeSpecificConfigInput
}

input DefaultGameConfigInput {
    timeLimit: Int
}

input GameCategorySpecificConfigInput {
    category: GAME_CATEGORY!
    blitz: DefaultGameConfigInput
    classical: DefaultGameConfigInput
    memory: DefaultGameConfigInput
    puzzle: PuzzleGameConfigInput
}

input GameTypeSpecificConfigInput {
    type: GAME_TYPE!
}

input DefaultGameModeConfigInput {
    numPlayers: Int
}

input GroupPlayGameConfigInput {
    maxTimePerQuestion: Int
    difficultyLevel: [Int!]
    maxGapBwGame: Int
    maxPlayers: Int
    minPlayers: Int
    numPlayers: Int
    questionTags: [String]
}

input ShowdownGameConfigInput {
    numberOfGames: Int
}

input GameModeSpecificConfigInput {
    mode: GAME_MODE!
    sumdayShowdown: ShowdownGameConfigInput
    onlineSearch: DefaultGameModeConfigInput
    onlineChallenge: DefaultGameModeConfigInput
    practice: DefaultGameModeConfigInput
    rushWithTime: DefaultGameModeConfigInput
    rushWithoutTime: DefaultGameModeConfigInput
    groupPlay: GroupPlayGameConfigInput
    playViaLink: DefaultGameModeConfigInput
    survivalSaturday: DefaultGameModeConfigInput
}

enum GAME_STATUS {
    CREATED
    READY
    STARTED
    PAUSED
    ENDED
    CANCELLED
}

enum GAME_TYPE {
    PLAY_ONLINE @deprecated
    PLAY_WITH_FRIEND @deprecated
    ONLINE_CHALLENGE @deprecated
    PRACTICE @deprecated
    SUMDAY_SHOWDOWN @deprecated
    GROUP_PLAY @deprecated
    ABILITY_DUELS @deprecated

    #Blitz Games Category
    FASTEST_FINGER
    DMAS
    DMAS_TIME_BANK

    # Classical Games Category
    DMAS_ABILITY

    # Memory Games Category
    FLASH_ANZAN

    # Puzzle Games Category
    CROSS_MATH_PUZZLE
    KEN_KEN_PUZZLE
}

enum PLAYER_STATUS {
    INVITED
    ACCEPTED
    REJECTED
}

input JoinGameInput {
    gameId: ID
}

input SubmitAnswerInput {
    gameId: ID
    questionId: String
    submittedValue: String
    isCorrect: Boolean
    inCorrectAttempts: Int
    timeOfSubmission: Date
    gameTypeSpecificAnswer: GameTypeSpecificAnswerInput
}

input FlashAnzanAnswerInput {
    maxScore: Int
}

input GameTypeSpecificAnswerInput {
    type: GAME_TYPE!
    flashAnzan: FlashAnzanAnswerInput
}

input SubmitFlashAnzanAnswerInput {
    gameId: ID
    questionIdentifier: String
    submittedValue: String
    isCorrect: Boolean
    timeOfSubmission: Date
    maxScore: Int
}

input StartGameInput {
    gameId: ID
}

type MinifiedGame {
    _id: ID!
    players: [Player!]!
    config: GameConfig
    leaderBoard: [LeaderBoardEntry]
    startTime: Time
    endTime: Time
}

type GetGamesOutput {
    games: [MinifiedGame]
    users: [UserPublicDetails]
}

type GetGamesByRatingOutput {
    games: [MinifiedGame]
    puzzleGames: [MinifiedPuzzleGame]
    users: [UserPublicDetails]
    totalCount: Int
}

input TimeRangeInput {
    startTime: Time
    endTime: Time
}

input PageInfoInput {
    pageNumber: Int
    rows: Int
}

input GetGamesInput {
    userId: ID
    timeRange: TimeRangeInput
    pageInfo: PageInfoInput
}

input GetGamesByRatingInput {
    userId: ID
    pageInfo: PageInfoInput
    ratingType: String
}

type GameDetailedAnalysis {
    game: Game!
    questions: [GameQuestionAnalysis!]!
}

type GameQuestionAnalysis {
    question: Question!
    avgTimes: [UserAvgTime!]!
    globalAvgTime: Float!
    globalBestTime: Float!
}

type UserAvgTime {
    userId: String!
    questionAvgTime: Float!
    presetAvgTime: Float!
    presetBestTime: Float!
}

input ChallengeUserInput {
    userId: ID
    gameConfig: GameConfigInput
}

type ChallengeOutput {
    gameId: ID!
    challengedBy: ID!
    gameConfig: GameConfig!
    createdAt: Time!
    status: ChallengeStatus
    opponent: User
}

enum ChallengeStatus {
    CHALLENGE_SENT
    CHALLENGE_ACCEPTED
    CHALLENGE_EXPIRED
    CHALLENGE_REJECTED

    GAME_CANCELLED
}

extend type Query {
    getGameById(gameId: ID): Game @auth
    getGamesByUser(payload: GetGamesInput): GetGamesOutput @auth
    getUserGamesByRatingType(
        payload: GetGamesByRatingInput
    ): GetGamesByRatingOutput @auth
    getGameDetailedAnalysis(gameId: ID): GameDetailedAnalysis @auth
}

extend type Mutation {
    createGame(gameConfig: GameConfigInput): Game @auth
    submitAnswer(answerInput: SubmitAnswerInput): Game @auth

    joinGame(joinGameInput: JoinGameInput): Game @auth
    leaveGame(gameId: ID!): Game @auth

    startGame(startGameInput: StartGameInput): Game @auth
    endGame(gameId: ID): Game
    removePlayer(gameId: ID!, playerId: ID!): Boolean! @auth

    startSearching(gameConfig: GameConfigInput): Boolean @auth
    abortSearching: Boolean @auth
    cancelGame(gameId: ID!): Boolean @auth

    requestRematch(gameId: ID!): Boolean! @auth
    acceptRematch(gameId: ID!): Game! @auth
    rejectRematch(gameId: ID!): Boolean! @auth
    cancelRematchRequest(gameId: ID!): Boolean! @auth

    challengeUser(challengeUserInput: ChallengeUserInput): Game @auth
    acceptChallenge(gameId: ID!): Game @auth
    rejectChallenge(gameId: ID!): Boolean @auth

    # Deprecated
    submitFlashAnzanAnswer(answerInput: SubmitFlashAnzanAnswerInput): Game
    @auth
    @deprecated
    endAbilityDuelsGame(gameId: ID): Game @auth @deprecated
    endGameForShowdown(gameId: ID): Game @auth @deprecated
}

type SubscriptionOutput {
    game: Game
    event: String
    question: Question
}

type SearchSubscriptionOutput {
    game: Game
    event: String
    opponent: User
}

extend type Subscription {
    searchPlayer(userId: ID): SearchSubscriptionOutput @deprecated
    gameEvent(gameId: ID): SubscriptionOutput @deprecated
    rematchRequest(gameId: ID!): RematchRequestOutput! @deprecated
}
