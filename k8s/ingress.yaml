apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: growthbook-ui-ingress
  namespace: growthbook
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "growthbook-ui-ip"
    networking.gke.io/managed-certificates: "growthbook-ui-cert"
spec:
  ingressClassName: gce
  rules:
    - host: "v0-growthbook.matiks.com"
      http:
        paths:
          - path: "/"
            pathType: Prefix
            backend:
              service:
                name: growthbook-app
                port:
                  number: 3000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: growthbook-api-ingress
  namespace: growthbook
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "growthbook-api-ip"
    networking.gke.io/managed-certificates: "growthbook-api-cert"
    # Enable Cloud CDN
    kubernetes.io/ingress.allow-http: "false"
    networking.gke.io/v1beta1.FrontendConfig: "growthbook-cdn-config"

spec:
  ingressClassName: gce
  rules:
    - host: "apiv0-growthbook.matiks.com"
      http:
        paths:
          - path: "/"
            pathType: Prefix
            backend:
              service:
                name: growthbook-app
                port:
                  number: 3100
---
apiVersion: networking.gke.io/v1beta1
kind: FrontendConfig
metadata:
  name: growthbook-cdn-config
  namespace: growthbook
spec:
  redirectToHttps:
    enabled: true
  sslPolicy: my-ssl-policy
  cdnPolicy:
    enabled: true
    cacheMode: CACHE_ALL_STATIC
    defaultTtl: 3600s
    clientTtl: 3600s
    serveWhileStale: 86400s
    cacheKeyPolicy:
      includeProtocol: true
      includeHost: true
      includeQueryString: true
