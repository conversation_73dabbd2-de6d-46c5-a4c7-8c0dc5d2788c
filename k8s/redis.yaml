apiVersion: apps/v1
kind: Deployment
metadata:
  name: growthbook-redis
  namespace: growthbook
spec:
  replicas: 1
  selector:
    matchLabels:
      app: growthbook-redis
  template:
    metadata:
      labels:
        app: growthbook-redis
    spec:
      containers:
        - name: redis
          image: redis:6.2-alpine
          ports:
            - containerPort: 6379
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 250m
              memory: 256Mi
---
apiVersion: v1
kind: Service
metadata:
  name: growthbook-redis
  namespace: growthbook
spec:
  selector:
    app: growthbook-redis
  ports:
    - port: 6379
      targetPort: 6379
