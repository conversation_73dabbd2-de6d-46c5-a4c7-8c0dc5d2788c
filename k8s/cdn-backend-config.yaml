apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: growthbook-cdn-backend-config
  namespace: growthbook
spec:
  # Enable Cloud CDN via BackendConfig
  cdn:
    enabled: true
    cachePolicy:
      includeHost: true
      includeProtocol: true
      includeQueryString: true
    # Cache mode - for feature flags we want to cache but allow quick updates
    cacheMode: "CACHE_ALL_STATIC"
    # TTL settings for optimal caching
    defaultTtl: 3600
    clientTtl: 3600
    maxTtl: 86400
  # Connection settings
  timeoutSec: 30
  connectionDraining:
    drainingTimeoutSec: 300
  # Health check settings
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 1
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /health
    port: 3100
