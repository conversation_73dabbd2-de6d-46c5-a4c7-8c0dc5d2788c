apiVersion: apps/v1
kind: Deployment
metadata:
  name: growthbook-app
  namespace: growthbook
spec:
  replicas: 1
  selector:
    matchLabels:
      app: growthbook-app
  template:
    metadata:
      labels:
        app: growthbook-app
    spec:
      containers:
        - name: growthbook
          image: growthbook/growthbook:latest
          ports:
            - containerPort: 3000
              name: http
            - containerPort: 3100
              name: api
          envFrom:
            - configMapRef:
                name: growthbook-config
            - secretRef:
                name: growthbook-secrets
          resources:
            # Specify initial resources - <PERSON>pi<PERSON> will adjust as needed
            requests:
              cpu: 500m
              memory: 512Mi
---
apiVersion: v1
kind: Service
metadata:
  name: growthbook-app-ui
  namespace: growthbook
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
spec:
  type: ClusterIP
  selector:
    app: growthbook-app
  ports:
    - port: 3000
      targetPort: 3000
      name: http
---
# Service for API (port 3100) - NodePort for ingress with BackendConfig
apiVersion: v1
kind: Service
metadata:
  name: growthbook-app-api
  namespace: growthbook
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"api": "growthbook-cdn-backend-config"}'
spec:
  type: NodePort
  selector:
    app: growthbook-app
  ports:
    - port: 3100
      targetPort: 3100
      name: api
