# ---------------------------------------------------------------------------------------------------------------------
# LABELS MODULE
# This module provides a consistent way to apply labels to resources across the Matiks infrastructure.
# It implements the tagging strategy defined in docs/tagging-strategy.md.
# ---------------------------------------------------------------------------------------------------------------------

locals {
  # Combine mandatory and optional labels
  labels = merge(
    {
      # Mandatory labels
      environment  = var.environment
      team         = var.team
      application  = var.application
      cost-center  = var.cost_center
      managed-by   = var.managed_by
      created-by   = var.created_by
      created-date = var.created_date
    },
    # Optional labels - only include if values are provided
    var.criticality != null ? { criticality = var.criticality } : {},
    var.data_classification != null ? { "data-classification" = var.data_classification } : {},
    var.backup != null ? { backup = var.backup } : {},
    var.compliance != null ? { compliance = var.compliance } : {},
    var.auto_delete != null ? { "auto-delete" = var.auto_delete } : {},
    var.owner != null ? { owner = var.owner } : {},
    
    # Project-specific labels
    var.project_id != null ? { "project-id" = var.project_id } : {},
    var.project_name != null ? { "project-name" = var.project_name } : {},
    var.business_unit != null ? { "business-unit" = var.business_unit } : {},
    
    # Environment-specific labels for production
    var.environment == "production" && var.sla != null ? { sla = var.sla } : {},
    var.environment == "production" && var.on_call_rotation != null ? { "on-call-rotation" = var.on_call_rotation } : {},
    
    # Environment-specific labels for development/testing
    contains(["development", "test", "staging"], var.environment) && var.expiry_date != null ? { "expiry-date" = var.expiry_date } : {},
    contains(["development", "test", "staging"], var.environment) && var.purpose != null ? { purpose = var.purpose } : {},
    
    # Additional custom labels
    var.additional_labels
  )
}