/**
 * Copyright 2023 Matiks
 *
 * Outputs for the GKE Security submodule.
 */

output "binary_authorization_policy_id" {
  description = "The ID of the Binary Authorization policy (if created)"
  value       = var.enable_binary_authorization && var.create_binary_authorization_policy ? google_binary_authorization_policy.policy[0].id : null
}

output "security_health_analytics_module_id" {
  description = "The ID of the Security Health Analytics custom module (if created)"
  value       = var.enable_security_posture && var.create_security_health_analytics_module ? google_security_center_source.custom_source[0].id : null
}

output "security_notification_config_id" {
  description = "The ID of the Security Command Center notification config (if created)"
  value       = var.enable_security_posture && var.create_security_notification ? google_scc_notification_config.scc_notification[0].id : null
}
