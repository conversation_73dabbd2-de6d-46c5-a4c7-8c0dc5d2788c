/**
 * # GKE Monitoring Submodule
 *
 * This submodule configures monitoring and logging for a Google Kubernetes Engine cluster.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = ">= 4.80.0"
    }
  }
}

# Create a Google Cloud Monitoring dashboard for the GKE cluster
resource "google_monitoring_dashboard" "gke_dashboard" {
  count = var.create_monitoring_dashboard ? 1 : 0
  
  project        = var.project_id
  dashboard_json = templatefile(
    "${path.module}/templates/gke_dashboard.json.tpl",
    {
      cluster_name = var.cluster_name
      location     = var.cluster_location
      project_id   = var.project_id
    }
  )
}

# Create a log metric for GKE errors
resource "google_logging_metric" "gke_error_metric" {
  count = var.create_error_log_metric ? 1 : 0
  
  project     = var.project_id
  name        = "${var.cluster_name}-error-metric"
  description = "Counts error logs from GKE cluster ${var.cluster_name}"
  filter      = "resource.type=\"k8s_cluster\" AND resource.labels.cluster_name=\"${var.cluster_name}\" AND resource.labels.location=\"${var.cluster_location}\" AND severity>=ERROR"
  
  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    labels {
      key         = "severity"
      value_type  = "STRING"
      description = "Error severity"
    }
  }
}

# Create an alert policy for GKE errors
resource "google_monitoring_alert_policy" "gke_error_alert" {
  count = var.create_error_alert_policy ? 1 : 0
  
  project      = var.project_id
  display_name = "${var.cluster_name} Error Alert"
  combiner     = "OR"
  
  conditions {
    display_name = "GKE Error Rate"
    
    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/${google_logging_metric.gke_error_metric[0].name}\" AND resource.type=\"k8s_cluster\" AND resource.labels.cluster_name=\"${var.cluster_name}\" AND resource.labels.location=\"${var.cluster_location}\""
      duration        = "60s"
      comparison      = "COMPARISON_GT"
      threshold_value = var.error_alert_threshold
      
      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
      
      trigger {
        count = 1
      }
    }
  }
  
  notification_channels = var.notification_channels
  
  documentation {
    content   = "GKE cluster ${var.cluster_name} in ${var.cluster_location} is experiencing errors."
    mime_type = "text/markdown"
  }
}

# Create a log sink for GKE logs to BigQuery
resource "google_logging_project_sink" "gke_logs_sink" {
  count = var.create_bigquery_log_sink ? 1 : 0
  
  project     = var.project_id
  name        = "${var.cluster_name}-logs-sink"
  destination = "bigquery.googleapis.com/projects/${var.project_id}/datasets/${var.bigquery_dataset_id}"
  filter      = "resource.type=\"k8s_cluster\" AND resource.labels.cluster_name=\"${var.cluster_name}\" AND resource.labels.location=\"${var.cluster_location}\""
  
  unique_writer_identity = true
}

# Grant the log sink service account permissions to write to BigQuery
resource "google_project_iam_member" "log_writer" {
  count = var.create_bigquery_log_sink ? 1 : 0
  
  project = var.project_id
  role    = "roles/bigquery.dataEditor"
  member  = google_logging_project_sink.gke_logs_sink[0].writer_identity
}
