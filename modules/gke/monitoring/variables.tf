/**
 * Copyright 2023 Matiks
 *
 * Variables for the GKE Monitoring submodule.
 */

variable "project_id" {
  description = "The ID of the project where resources will be created"
  type        = string
}

variable "cluster_name" {
  description = "The name of the GKE cluster"
  type        = string
}

variable "cluster_location" {
  description = "The location (region) of the GKE cluster"
  type        = string
}

variable "monitoring_components" {
  description = "List of monitoring components to enable"
  type        = list(string)
  default     = ["SYSTEM_COMPONENTS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
}

variable "logging_components" {
  description = "List of logging components to enable"
  type        = list(string)
  default     = ["SYSTEM_COMPONENTS", "WORKLOADS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
}

variable "enable_managed_prometheus" {
  description = "Enable Google Cloud Managed Service for Prometheus"
  type        = bool
  default     = true
}

variable "enable_advanced_datapath_observability" {
  description = "Enable advanced datapath observability metrics for Prometheus application monitoring"
  type        = bool
  default     = true
}

variable "create_monitoring_dashboard" {
  description = "Whether to create a monitoring dashboard for the GKE cluster"
  type        = bool
  default     = true
}

variable "create_error_log_metric" {
  description = "Whether to create a log metric for GKE errors"
  type        = bool
  default     = true
}

variable "create_error_alert_policy" {
  description = "Whether to create an alert policy for GKE errors"
  type        = bool
  default     = true
}

variable "error_alert_threshold" {
  description = "The threshold for the error alert policy"
  type        = number
  default     = 0.1
}

variable "notification_channels" {
  description = "The notification channels to send alerts to"
  type        = list(string)
  default     = []
}

variable "create_bigquery_log_sink" {
  description = "Whether to create a BigQuery log sink for GKE logs"
  type        = bool
  default     = false
}

variable "bigquery_dataset_id" {
  description = "The ID of the BigQuery dataset for log export"
  type        = string
  default     = ""
}
