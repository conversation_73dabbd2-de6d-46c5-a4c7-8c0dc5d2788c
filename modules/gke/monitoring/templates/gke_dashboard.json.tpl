{"displayName": "GKE Cluster Dashboard - ${cluster_name}", "mosaicLayout": {"columns": 12, "tiles": [{"width": 6, "height": 4, "widget": {"title": "CPU Utilization", "xyChart": {"chartOptions": {"mode": "COLOR"}, "dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"k8s_container\" AND resource.labels.cluster_name=\"${cluster_name}\" AND resource.labels.location=\"${location}\" AND metric.type=\"kubernetes.io/container/cpu/core_usage_time\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["resource.labels.namespace_name"]}}}, "plotType": "LINE", "minAlignmentPeriod": "60s", "targetAxis": "Y1"}], "timeshiftDuration": "0s", "yAxis": {"label": "y1Axis", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "widget": {"title": "Memory Usage", "xyChart": {"chartOptions": {"mode": "COLOR"}, "dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"k8s_container\" AND resource.labels.cluster_name=\"${cluster_name}\" AND resource.labels.location=\"${location}\" AND metric.type=\"kubernetes.io/container/memory/used_bytes\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["resource.labels.namespace_name"]}}}, "plotType": "LINE", "minAlignmentPeriod": "60s", "targetAxis": "Y1"}], "timeshiftDuration": "0s", "yAxis": {"label": "y1Axis", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "widget": {"title": "Pod Count", "xyChart": {"chartOptions": {"mode": "COLOR"}, "dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"k8s_pod\" AND resource.labels.cluster_name=\"${cluster_name}\" AND resource.labels.location=\"${location}\" AND metric.type=\"kubernetes.io/pod/status/phase\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_COUNT", "groupByFields": ["metric.labels.phase"]}}}, "plotType": "LINE", "minAlignmentPeriod": "60s", "targetAxis": "Y1"}], "timeshiftDuration": "0s", "yAxis": {"label": "y1Axis", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "widget": {"title": "Network Traffic", "xyChart": {"chartOptions": {"mode": "COLOR"}, "dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"k8s_pod\" AND resource.labels.cluster_name=\"${cluster_name}\" AND resource.labels.location=\"${location}\" AND metric.type=\"kubernetes.io/pod/network/received_bytes_count\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["resource.labels.namespace_name"]}}}, "plotType": "LINE", "minAlignmentPeriod": "60s", "targetAxis": "Y1", "legendTemplate": "Received"}, {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"k8s_pod\" AND resource.labels.cluster_name=\"${cluster_name}\" AND resource.labels.location=\"${location}\" AND metric.type=\"kubernetes.io/pod/network/sent_bytes_count\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["resource.labels.namespace_name"]}}}, "plotType": "LINE", "minAlignmentPeriod": "60s", "targetAxis": "Y1", "legendTemplate": "<PERSON><PERSON>"}], "timeshiftDuration": "0s", "yAxis": {"label": "y1Axis", "scale": "LINEAR"}}}}, {"width": 12, "height": 4, "widget": {"title": "<PERSON><PERSON><PERSON>", "logsPanel": {"filter": "resource.type=\"k8s_cluster\" AND resource.labels.cluster_name=\"${cluster_name}\" AND resource.labels.location=\"${location}\" AND severity>=ERROR", "resourceNames": ["projects/${project_id}"]}}}]}}