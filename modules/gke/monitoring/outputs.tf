/**
 * Copyright 2023 Matiks
 *
 * Outputs for the GKE Monitoring submodule.
 */

output "dashboard_url" {
  description = "The URL of the GKE monitoring dashboard (if created)"
  value       = var.create_monitoring_dashboard ? "https://console.cloud.google.com/monitoring/dashboards/custom/${element(split("/", google_monitoring_dashboard.gke_dashboard[0].id), length(split("/", google_monitoring_dashboard.gke_dashboard[0].id)) - 1)}" : null
}

output "error_metric_name" {
  description = "The name of the GKE error metric (if created)"
  value       = var.create_error_log_metric ? google_logging_metric.gke_error_metric[0].name : null
}

output "error_alert_policy_name" {
  description = "The name of the GKE error alert policy (if created)"
  value       = var.create_error_alert_policy ? google_monitoring_alert_policy.gke_error_alert[0].name : null
}

output "log_sink_name" {
  description = "The name of the GKE log sink (if created)"
  value       = var.create_bigquery_log_sink ? google_logging_project_sink.gke_logs_sink[0].name : null
}

output "log_sink_destination" {
  description = "The destination of the GKE log sink (if created)"
  value       = var.create_bigquery_log_sink ? google_logging_project_sink.gke_logs_sink[0].destination : null
}
