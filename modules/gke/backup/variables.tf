/**
 * Copyright 2023 Matiks
 *
 * Variables for the GKE Backup submodule.
 */

variable "project_id" {
  description = "The ID of the project where resources will be created"
  type        = string
}

variable "cluster_name" {
  description = "The name of the GKE cluster"
  type        = string
}

variable "cluster_location" {
  description = "The location (region) of the GKE cluster"
  type        = string
}

variable "backup_plan_name" {
  description = "The name of the backup plan"
  type        = string
}

variable "backup_schedule" {
  description = "The cron schedule for backups"
  type        = string
  default     = "0 9 * * *"  # Daily at 9 AM
}

variable "retention_days" {
  description = "The number of days to retain backups"
  type        = number
  default     = 14
}

variable "backup_delete_lock_days" {
  description = "The number of days to lock backups from deletion"
  type        = number
  default     = 0
}

variable "include_volume_data" {
  description = "Whether to include volume data in backups"
  type        = bool
  default     = true
}

variable "include_secrets" {
  description = "Whether to include secrets in backups"
  type        = bool
  default     = true
}

variable "backup_all_namespaces" {
  description = "Whether to backup all namespaces"
  type        = bool
  default     = true
}

variable "backup_namespaces" {
  description = "List of namespaces to backup if not backing up all namespaces"
  type        = list(string)
  default     = []
}

variable "create_restore_plan" {
  description = "Whether to create a restore plan"
  type        = bool
  default     = true
}

variable "restore_all_namespaces" {
  description = "Whether to restore all namespaces"
  type        = bool
  default     = true
}

variable "restore_namespaces" {
  description = "List of namespaces to restore if not restoring all namespaces"
  type        = list(string)
  default     = []
}

variable "volume_data_restore_policy" {
  description = "Policy for restoring volume data"
  type        = string
  default     = "RESTORE_VOLUME_DATA_FROM_BACKUP"
  validation {
    condition     = contains(["RESTORE_VOLUME_DATA_FROM_BACKUP", "REUSE_VOLUME_HANDLE_FROM_BACKUP", "NO_VOLUME_DATA_RESTORATION"], var.volume_data_restore_policy)
    error_message = "Volume data restore policy must be one of: RESTORE_VOLUME_DATA_FROM_BACKUP, REUSE_VOLUME_HANDLE_FROM_BACKUP, NO_VOLUME_DATA_RESTORATION."
  }
}

variable "cluster_resource_conflict_policy" {
  description = "Policy for handling conflicts with cluster-scoped resources"
  type        = string
  default     = "USE_EXISTING_VERSION"
  validation {
    condition     = contains(["USE_EXISTING_VERSION", "USE_BACKUP_VERSION"], var.cluster_resource_conflict_policy)
    error_message = "Cluster resource conflict policy must be one of: USE_EXISTING_VERSION, USE_BACKUP_VERSION."
  }
}

variable "namespaced_resource_restore_mode" {
  description = "Mode for restoring namespaced resources"
  type        = string
  default     = "DELETE_AND_RESTORE"
  validation {
    condition     = contains(["DELETE_AND_RESTORE", "FAIL_ON_CONFLICT"], var.namespaced_resource_restore_mode)
    error_message = "Namespaced resource restore mode must be one of: DELETE_AND_RESTORE, FAIL_ON_CONFLICT."
  }
}
