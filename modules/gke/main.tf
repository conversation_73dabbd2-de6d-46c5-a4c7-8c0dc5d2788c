# main.tf for gke module
# Orchestrates the creation of GKE clusters by calling the gke_autopilot_cluster module.

module "gke_autopilot_cluster" {
  source = var.autopilot_module_source # Use variable for source path

  for_each = var.clusters # Iterate over the map of cluster definitions

  # Map inputs from the cluster definition object in the var.clusters map
  project_id             = each.value.project_id
  name                   = each.value.name
  region                 = each.value.region
  network_self_link      = each.value.network_self_link
  subnetwork_self_link   = each.value.subnetwork_self_link
  release_channel        = each.value.release_channel
  description            = each.value.description
  pods_ip_range_name     = each.value.pods_ip_range_name
  services_ip_range_name = each.value.services_ip_range_name
  labels                 = each.value.labels

  # depends_on is implicitly handled by passing outputs from project/network modules
  # as inputs (project_id, network_self_link, subnetwork_self_link)
}