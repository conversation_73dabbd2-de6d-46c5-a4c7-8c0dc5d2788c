/**
 * Copyright 2023 Matiks
 *
 * Outputs for the GKE Autopilot submodule.
 */

output "cluster_name" {
  description = "The name of the GKE cluster"
  value       = google_container_cluster.autopilot_cluster.name
}

output "cluster_endpoint" {
  description = "The public endpoint of the GKE cluster's control plane"
  value       = google_container_cluster.autopilot_cluster.endpoint
  sensitive   = true
}

output "cluster_location" {
  description = "The location (region) of the GKE cluster"
  value       = google_container_cluster.autopilot_cluster.location
}

output "workload_identity_pool" {
  description = "The Workload Identity Pool for the cluster"
  value       = google_container_cluster.autopilot_cluster.workload_identity_config[0].workload_pool
}

output "cluster_id" {
  description = "The full ID of the GKE cluster"
  value       = google_container_cluster.autopilot_cluster.id
}

output "secret_manager_enabled" {
  description = "Whether Secret Manager add-on is enabled for the cluster"
  value       = google_container_cluster.autopilot_cluster.secret_manager_config[0].enabled
}

output "managed_prometheus_enabled" {
  description = "Whether Managed Prometheus is enabled for the cluster"
  value       = google_container_cluster.autopilot_cluster.monitoring_config[0].managed_prometheus[0].enabled
}

output "master_version" {
  description = "The current version of the master in the cluster"
  value       = google_container_cluster.autopilot_cluster.master_version
}

output "ca_certificate" {
  description = "The cluster CA certificate (base64 encoded)"
  value       = google_container_cluster.autopilot_cluster.master_auth[0].cluster_ca_certificate
  sensitive   = true
}
