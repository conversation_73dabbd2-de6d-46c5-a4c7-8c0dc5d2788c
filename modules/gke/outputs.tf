/**
 * Copyright 2023 Matiks
 *
 * Outputs for the GKE module.
 */

output "cluster_name" {
  description = "The name of the GKE cluster"
  value       = module.autopilot_cluster.cluster_name
}

output "cluster_endpoint" {
  description = "The endpoint of the GKE cluster"
  value       = module.autopilot_cluster.cluster_endpoint
  sensitive   = true
}

output "cluster_location" {
  description = "The location (region) of the GKE cluster"
  value       = module.autopilot_cluster.cluster_location
}

output "cluster_id" {
  description = "The ID of the GKE cluster"
  value       = module.autopilot_cluster.cluster_id
}

output "service_account_email" {
  description = "The email of the service account used by the GKE cluster"
  value       = var.service_account_create ? module.service_account[0].email : var.service_account_email
}

output "workload_identity_pool" {
  description = "The Workload Identity Pool for the cluster"
  value       = module.autopilot_cluster.workload_identity_pool
}

output "managed_prometheus_enabled" {
  description = "Whether Managed Prometheus is enabled for the cluster"
  value       = var.enable_managed_prometheus
}

output "secret_manager_enabled" {
  description = "Whether Secret Manager is enabled for the cluster"
  value       = var.enable_secret_manager
}

output "backup_plan_name" {
  description = "The name of the backup plan (if backup is enabled)"
  value       = var.enable_backup_agent ? module.backup[0].backup_plan_name : null
}

output "backup_plan_id" {
  description = "The ID of the backup plan (if backup is enabled)"
  value       = var.enable_backup_agent ? module.backup[0].backup_plan_id : null
}
