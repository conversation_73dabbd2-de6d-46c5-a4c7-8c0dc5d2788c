/**
 * Copyright 2023 Matiks
 *
 * Variables for the GKE module.
 */

variable "project_id" {
  description = "The ID of the project where resources will be created"
  type        = string
}

variable "name" {
  description = "The name of the GKE cluster"
  type        = string
}

variable "region" {
  description = "The region where the GKE cluster will be created"
  type        = string
}

variable "network_self_link" {
  description = "The self-link of the VPC network"
  type        = string
}

variable "subnetwork_self_link" {
  description = "The self-link of the subnetwork"
  type        = string
}

variable "environment" {
  description = "The environment (prod, nonprod, dev)"
  type        = string
  default     = "prod"
  validation {
    condition     = contains(["prod", "nonprod", "dev"], var.environment)
    error_message = "Environment must be one of: prod, nonprod, dev."
  }
}

variable "release_channel" {
  description = "The GKE release channel (STABLE, REGULAR, RAPID)"
  type        = string
  default     = "STABLE"
  validation {
    condition     = contains(["STABLE", "REGULAR", "RAPID"], var.release_channel)
    error_message = "Release channel must be one of: STABLE, REGULAR, RAPID."
  }
}

variable "description" {
  description = "An optional description of the GKE cluster"
  type        = string
  default     = "GKE Autopilot Cluster managed by Terraform"
}

variable "pods_ip_range_name" {
  description = "The secondary IP range name for GKE pods"
  type        = string
  default     = "gke-pods"
}

variable "services_ip_range_name" {
  description = "The secondary IP range name for GKE services"
  type        = string
  default     = "gke-services"
}

variable "enable_private_nodes" {
  description = "Whether nodes have internal IP addresses only"
  type        = bool
  default     = true
}

variable "enable_private_endpoint" {
  description = "Whether the master's internal IP address is used as the cluster endpoint"
  type        = bool
  default     = false
}

variable "master_ipv4_cidr_block" {
  description = "The IP range in CIDR notation for the master network"
  type        = string
  default     = "**********/28"
}

variable "master_global_access" {
  description = "Whether the cluster master is accessible globally (from any region)"
  type        = bool
  default     = true
}

variable "master_authorized_networks" {
  description = "List of master authorized networks"
  type = list(object({
    cidr_block   = string
    display_name = string
  }))
  default = null
}

variable "enable_confidential_nodes" {
  description = "Enable Confidential Nodes for this cluster"
  type        = bool
  default     = false
}

variable "enable_security_posture" {
  description = "Enable Security Posture API for this cluster"
  type        = bool
  default     = true
}

variable "enable_binary_authorization" {
  description = "Enable Binary Authorization for this cluster"
  type        = bool
  default     = false
}

variable "binary_authorization_evaluation_mode" {
  description = "Mode of operation for Binary Authorization policy evaluation"
  type        = string
  default     = "PROJECT_SINGLETON_POLICY_ENFORCE"
}

variable "enable_managed_prometheus" {
  description = "Enable Google Cloud Managed Service for Prometheus"
  type        = bool
  default     = true
}

variable "enable_advanced_datapath_observability" {
  description = "Enable advanced datapath observability metrics for Prometheus application monitoring"
  type        = bool
  default     = true
}

variable "monitoring_components" {
  description = "List of monitoring components to enable"
  type        = list(string)
  default     = ["SYSTEM_COMPONENTS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
}

variable "logging_components" {
  description = "List of logging components to enable"
  type        = list(string)
  default     = ["SYSTEM_COMPONENTS", "WORKLOADS", "APISERVER", "CONTROLLER_MANAGER", "SCHEDULER"]
}

variable "enable_backup_agent" {
  description = "Enable Backup for GKE agent"
  type        = bool
  default     = false
}

variable "backup_plan_name" {
  description = "The name of the backup plan"
  type        = string
  default     = ""
}

variable "backup_schedule" {
  description = "The cron schedule for backups"
  type        = string
  default     = "0 9 * * *"  # Daily at 9 AM
}

variable "backup_retention_days" {
  description = "The number of days to retain backups"
  type        = number
  default     = 14
}

variable "service_account_create" {
  description = "Whether to create a dedicated service account for the GKE cluster"
  type        = bool
  default     = true
}

variable "service_account_name" {
  description = "The name of the service account to create"
  type        = string
  default     = "gke-cluster-sa"
}

variable "service_account_email" {
  description = "The email of an existing service account to use (if service_account_create is false)"
  type        = string
  default     = null
}

variable "labels" {
  description = "A map of labels to apply to the GKE cluster"
  type        = map(string)
  default     = {}
}

variable "deletion_protection" {
  description = "Whether or not to allow Terraform to destroy the cluster"
  type        = bool
  default     = true
}

variable "datapath_provider" {
  description = "The desired datapath provider for this cluster"
  type        = string
  default     = "ADVANCED_DATAPATH"
}

variable "enable_fleet" {
  description = "Whether to enable Fleet features on the cluster"
  type        = bool
  default     = true
}

variable "fleet_project" {
  description = "The project ID where the Fleet is hosted. If empty, the cluster's project is used."
  type        = string
  default     = ""
}

variable "enable_secret_manager" {
  description = "Enable Secret Manager add-on for the cluster"
  type        = bool
  default     = true
}

variable "enable_cost_allocation" {
  description = "Enable Cost Allocation to get cost breakdowns by Kubernetes resources"
  type        = bool
  default     = true
}

variable "enable_vertical_pod_autoscaling" {
  description = "Enable Vertical Pod Autoscaling for the cluster"
  type        = bool
  default     = true
}
