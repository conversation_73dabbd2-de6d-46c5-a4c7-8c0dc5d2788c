/**
 * Copyright 2023 Matiks
 *
 * Outputs for the Subnet submodule.
 */

output "subnet_id" {
  description = "The ID of the subnet"
  value       = google_compute_subnetwork.subnet.id
}

output "subnet_self_link" {
  description = "The URI of the subnet"
  value       = google_compute_subnetwork.subnet.self_link
}

output "subnet_name" {
  description = "The name of the subnet"
  value       = google_compute_subnetwork.subnet.name
}

output "subnet_region" {
  description = "The region of the subnet"
  value       = google_compute_subnetwork.subnet.region
}

output "subnet_ip_cidr_range" {
  description = "The IP CIDR range of the subnet"
  value       = google_compute_subnetwork.subnet.ip_cidr_range
}

output "secondary_ranges" {
  description = "The secondary IP ranges of the subnet"
  value       = google_compute_subnetwork.subnet.secondary_ip_range
}

output "gateway_address" {
  description = "The gateway address of the subnet"
  value       = google_compute_subnetwork.subnet.gateway_address
}
