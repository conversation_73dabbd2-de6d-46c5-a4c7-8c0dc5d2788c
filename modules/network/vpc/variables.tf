/**
 * Copyright 2023 Matiks
 *
 * Variables for the VPC submodule.
 */

variable "project_id" {
  description = "The ID of the project where the VPC will be created"
  type        = string
}

variable "name" {
  description = "The name of the VPC network"
  type        = string
}

variable "description" {
  description = "An optional description of the VPC network"
  type        = string
  default     = null
}

variable "auto_create_subnetworks" {
  description = "Whether to create subnetworks automatically"
  type        = bool
  default     = false
}

variable "routing_mode" {
  description = "The network routing mode (GLOBAL or REGIONAL)"
  type        = string
  default     = "GLOBAL"
  validation {
    condition     = contains(["GLOBAL", "REGIONAL"], var.routing_mode)
    error_message = "Routing mode must be either GLOBAL or REGIONAL."
  }
}

variable "mtu" {
  description = "Maximum Transmission Unit in bytes"
  type        = number
  default     = 1460
  validation {
    condition     = var.mtu >= 1300 && var.mtu <= 8896
    error_message = "MTU must be between 1300 and 8896 bytes."
  }
}

variable "delete_default_routes_on_create" {
  description = "Whether to delete the default routes when the network is created"
  type        = bool
  default     = false
}

variable "labels" {
  description = "A map of labels to apply to the VPC"
  type        = map(string)
  default     = {}
}

variable "timeouts" {
  description = "Timeouts for VPC operations"
  type = object({
    create = optional(string)
    update = optional(string)
    delete = optional(string)
  })
  default = null
}
