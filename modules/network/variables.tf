/**
 * Copyright 2023 Matiks
 *
 * Variables for the Network module.
 */

variable "project_id" {
  description = "The ID of the project where resources will be created"
  type        = string
}

variable "name" {
  description = "The name of the VPC network"
  type        = string
}

variable "description" {
  description = "An optional description of the VPC network"
  type        = string
  default     = null
}

variable "auto_create_subnetworks" {
  description = "Whether to create subnetworks automatically"
  type        = bool
  default     = false
}

variable "routing_mode" {
  description = "The network routing mode (GLOBAL or REGIONAL)"
  type        = string
  default     = "GLOBAL"
  validation {
    condition     = contains(["GLOBAL", "REGIONAL"], var.routing_mode)
    error_message = "Routing mode must be either GLOBAL or REGIONAL."
  }
}

variable "mtu" {
  description = "Maximum Transmission Unit in bytes"
  type        = number
  default     = 1460
  validation {
    condition     = var.mtu >= 1300 && var.mtu <= 8896
    error_message = "MTU must be between 1300 and 8896 bytes."
  }
}

variable "labels" {
  description = "A map of labels to apply to resources"
  type        = map(string)
  default     = {}
}

variable "subnets" {
  description = "List of subnet configurations"
  type = list(object({
    name          = string
    ip_cidr_range = string
    region        = string
    description   = optional(string)
    private_access = optional(bool, true)
    secondary_ip_ranges = optional(list(object({
      range_name    = string
      ip_cidr_range = string
    })), [])
  }))
  default = []
}

variable "firewall_rules" {
  description = "List of firewall rule configurations"
  type = list(object({
    name        = string
    description = optional(string)
    direction   = optional(string, "INGRESS")
    priority    = optional(number, 1000)
    source_ranges = optional(list(string))
    source_tags = optional(list(string))
    target_tags = optional(list(string))
    source_service_accounts = optional(list(string))
    target_service_accounts = optional(list(string))
    allow = optional(list(object({
      protocol = string
      ports    = optional(list(string))
    })), [])
    deny = optional(list(object({
      protocol = string
      ports    = optional(list(string))
    })), [])
    log_config = optional(object({
      metadata = string
    }))
  }))
  default = []
}

variable "nat_config" {
  description = "NAT configuration"
  type = object({
    region                             = string
    nat_ip_allocate_option             = optional(string, "AUTO_ONLY")
    source_subnetwork_ip_ranges_to_nat = optional(string, "ALL_SUBNETWORKS_ALL_IP_RANGES")
    nat_ips                            = optional(list(string))
    min_ports_per_vm                   = optional(number)
    udp_idle_timeout_sec               = optional(number)
    icmp_idle_timeout_sec              = optional(number)
    tcp_established_idle_timeout_sec   = optional(number)
    tcp_transitory_idle_timeout_sec    = optional(number)
    log_config = optional(object({
      enable = bool
      filter = string
    }))
  })
  default = null
}

variable "dns_config" {
  description = "DNS configuration"
  type = object({
    dns_zones = list(object({
      name        = string
      dns_name    = string
      description = optional(string)
      visibility  = optional(string, "private")
    }))
    dns_records = list(object({
      zone_name  = string
      name       = string
      type       = string
      ttl        = number
      rrdatas    = list(string)
    }))
  })
  default = null
}
