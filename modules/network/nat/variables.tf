/**
 * Copyright 2023 Matiks
 *
 * Variables for the NAT submodule.
 */

variable "project_id" {
  description = "The ID of the project where the NAT will be created"
  type        = string
}

variable "network_id" {
  description = "The ID of the VPC network where the NAT will be created"
  type        = string
}

variable "region" {
  description = "The region where the NAT will be created"
  type        = string
}

variable "router_name" {
  description = "The name of the Cloud Router"
  type        = string
}

variable "router_description" {
  description = "An optional description of the Cloud Router"
  type        = string
  default     = null
}

variable "nat_name" {
  description = "The name of the Cloud NAT gateway"
  type        = string
}

variable "nat_ip_allocate_option" {
  description = "How external IPs should be allocated for the NAT (AUTO_ONLY or MANUAL_ONLY)"
  type        = string
  default     = "AUTO_ONLY"
  validation {
    condition     = contains(["AUTO_ONLY", "MANUAL_ONLY"], var.nat_ip_allocate_option)
    error_message = "NAT IP allocate option must be either AUTO_ONLY or MANUAL_ONLY."
  }
}

variable "source_subnetwork_ip_ranges_to_nat" {
  description = "How NAT should be configured per subnetwork"
  type        = string
  default     = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  validation {
    condition     = contains(["ALL_SUBNETWORKS_ALL_IP_RANGES", "ALL_SUBNETWORKS_ALL_PRIMARY_IP_RANGES", "LIST_OF_SUBNETWORKS"], var.source_subnetwork_ip_ranges_to_nat)
    error_message = "Source subnetwork IP ranges to NAT must be one of: ALL_SUBNETWORKS_ALL_IP_RANGES, ALL_SUBNETWORKS_ALL_PRIMARY_IP_RANGES, LIST_OF_SUBNETWORKS."
  }
}

variable "nat_ips" {
  description = "List of self links of external IPs to use for NAT (required if nat_ip_allocate_option is MANUAL_ONLY)"
  type        = list(string)
  default     = null
}

variable "min_ports_per_vm" {
  description = "Minimum number of ports allocated to a VM from the NAT"
  type        = number
  default     = null
}

variable "udp_idle_timeout_sec" {
  description = "Timeout for UDP connections in seconds"
  type        = number
  default     = null
}

variable "icmp_idle_timeout_sec" {
  description = "Timeout for ICMP connections in seconds"
  type        = number
  default     = null
}

variable "tcp_established_idle_timeout_sec" {
  description = "Timeout for established TCP connections in seconds"
  type        = number
  default     = null
}

variable "tcp_transitory_idle_timeout_sec" {
  description = "Timeout for transitory TCP connections in seconds"
  type        = number
  default     = null
}

variable "log_config" {
  description = "Logging configuration for the NAT"
  type = object({
    enable = bool
    filter = string
  })
  default = null
}

variable "subnetworks" {
  description = "List of subnetwork configurations for NAT (required if source_subnetwork_ip_ranges_to_nat is LIST_OF_SUBNETWORKS)"
  type = list(object({
    name                     = string
    source_ip_ranges_to_nat  = list(string)
    secondary_ip_range_names = optional(list(string))
  }))
  default = null
}

variable "timeouts" {
  description = "Timeouts for NAT operations"
  type = object({
    create = optional(string)
    update = optional(string)
    delete = optional(string)
  })
  default = null
}
