/**
 * Copyright 2023 Matiks
 *
 * Outputs for the Network module.
 */

output "network_id" {
  description = "The ID of the VPC network"
  value       = module.vpc.network_id
}

output "network_self_link" {
  description = "The URI of the VPC network"
  value       = module.vpc.network_self_link
}

output "network_name" {
  description = "The name of the VPC network"
  value       = module.vpc.network_name
}

output "subnets" {
  description = "The created subnet resources"
  value       = module.subnet
}

output "subnet_ids" {
  description = "Map of subnet names to IDs"
  value       = { for name, subnet in module.subnet : name => subnet.subnet_id }
}

output "subnet_self_links" {
  description = "Map of subnet names to self links"
  value       = { for name, subnet in module.subnet : name => subnet.subnet_self_link }
}

output "subnet_regions" {
  description = "Map of subnet names to regions"
  value       = { for name, subnet in module.subnet : name => subnet.subnet_region }
}

output "subnet_ip_cidr_ranges" {
  description = "Map of subnet names to IP CIDR ranges"
  value       = { for name, subnet in module.subnet : name => subnet.subnet_ip_cidr_range }
}

output "secondary_ranges" {
  description = "Map of subnet names to secondary IP ranges"
  value       = { for name, subnet in module.subnet : name => subnet.secondary_ranges }
}

output "firewall_rules" {
  description = "The created firewall rule resources"
  value       = module.firewall
}

output "nat_gateway" {
  description = "The created NAT gateway resource"
  value       = var.nat_config != null ? module.nat[0] : null
}

output "dns_zones" {
  description = "The created DNS zone resources"
  value       = var.dns_config != null ? module.dns[0].dns_zones : null
}

output "dns_records" {
  description = "The created DNS record resources"
  value       = var.dns_config != null ? module.dns[0].dns_records : null
}
