# Network Module

This module manages Google Cloud Platform network resources, providing a structured approach to creating and managing VPC networks, subnets, firewall rules, NAT gateways, and DNS configurations.

## Features

- VPC network creation with customizable options
- Subnet management with separate configurations for production and non-production environments
- Firewall rule management with default deny approach and proper rule priorities
- Cloud NAT configuration for outbound connectivity
- Cloud DNS configuration for domain management
- Secondary IP range configuration for GKE pods and services
- Shared VPC configuration for service projects

## Usage

```hcl
module "network" {
  source = "../../modules/network"

  # Required variables
  project_id = "my-project-id"
  name       = "my-vpc-network"
  
  # Optional variables
  auto_create_subnetworks = false
  routing_mode            = "GLOBAL"
  mtu                     = 1460
  
  # Subnet configuration
  subnets = [
    {
      name          = "subnet-prod-1"
      ip_cidr_range = "10.0.0.0/24"
      region        = "us-west1"
      secondary_ip_ranges = [
        {
          range_name    = "gke-pods"
          ip_cidr_range = "********/16"
        },
        {
          range_name    = "gke-services"
          ip_cidr_range = "********/20"
        }
      ]
    },
    {
      name          = "subnet-non-prod-2"
      ip_cidr_range = "*********/24"
      region        = "us-west1"
      secondary_ip_ranges = [
        {
          range_name    = "gke-pods"
          ip_cidr_range = "*********/16"
        },
        {
          range_name    = "gke-services"
          ip_cidr_range = "*********/20"
        }
      ]
    }
  ]
  
  # Firewall configuration
  firewall_rules = [
    {
      name        = "vpc-allow-internal"
      description = "Allow internal traffic"
      direction   = "INGRESS"
      priority    = 1000
      source_ranges = ["10.0.0.0/8"]
      allow = [
        {
          protocol = "tcp"
          ports    = ["0-65535"]
        },
        {
          protocol = "udp"
          ports    = ["0-65535"]
        },
        {
          protocol = "icmp"
        }
      ]
    },
    {
      name        = "vpc-deny-all-ingress"
      description = "Deny all ingress traffic"
      direction   = "INGRESS"
      priority    = 65000
      deny = [
        {
          protocol = "all"
        }
      ]
    }
  ]
  
  # NAT configuration
  nat_config = {
    region                             = "us-west1"
    nat_ip_allocate_option             = "AUTO_ONLY"
    source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  }
}
```

## Submodules

This module includes the following submodules:

- **VPC**: Creates and manages VPC networks
- **Subnet**: Creates and manages subnets with secondary IP ranges
- **Firewall**: Creates and manages firewall rules with proper priorities
- **NAT**: Creates and manages Cloud NAT gateways
- **DNS**: Creates and manages Cloud DNS zones and records

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.3.0 |
| google | >= 4.80.0 |
| google-beta | >= 4.80.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_id | The ID of the project where resources will be created | `string` | n/a | yes |
| name | The name of the VPC network | `string` | n/a | yes |
| auto_create_subnetworks | Whether to create subnetworks automatically | `bool` | `false` | no |
| routing_mode | The network routing mode | `string` | `"GLOBAL"` | no |
| mtu | Maximum Transmission Unit in bytes | `number` | `1460` | no |
| subnets | List of subnet configurations | `list(object)` | `[]` | no |
| firewall_rules | List of firewall rule configurations | `list(object)` | `[]` | no |
| nat_config | NAT configuration | `object` | `null` | no |
| dns_config | DNS configuration | `object` | `null` | no |

## Outputs

| Name | Description |
|------|-------------|
| network_id | The ID of the VPC network |
| network_self_link | The URI of the VPC network |
| subnets | The created subnet resources |
| subnet_ids | Map of subnet names to IDs |
| subnet_self_links | Map of subnet names to self links |
| secondary_ranges | Map of subnet names to secondary IP ranges |
| firewall_rules | The created firewall rule resources |
| nat_gateway | The created NAT gateway resource |
| dns_zones | The created DNS zone resources |

## Examples

- [Basic VPC Network](../../examples/network/basic)
- [Shared VPC Configuration](../../examples/network/shared-vpc)
- [Multi-Region Network](../../examples/network/multi-region)
- [GKE Network Configuration](../../examples/network/gke-network)

## Notes

- The default approach for firewall rules is to deny all ingress traffic and explicitly allow required traffic
- Subnet names should follow the pattern `subnet-[env]-[number]` (e.g., `subnet-prod-1`, `subnet-non-prod-2`)
- Production GKE clusters should use `subnet-prod-1` and development clusters should use `subnet-non-prod-2`
- Firewall rule priorities should follow these guidelines:
  - 1000-1999: Critical infrastructure rules
  - 2000-4999: Service-specific rules
  - 5000-9999: Basic connectivity rules
  - 65000: Default deny rules
