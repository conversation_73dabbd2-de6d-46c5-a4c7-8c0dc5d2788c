/**
 * # Monitoring Module
 *
 * This module manages Google Cloud Platform monitoring resources, providing a structured approach to creating and managing dashboards, alert policies, uptime checks, and service level objectives (SLOs).
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = ">= 4.80.0"
    }
  }
}

# Local variables
locals {
  # Process dashboard configurations
  dashboards = { for dashboard in var.dashboards : dashboard.display_name => dashboard }
  
  # Process alert policy configurations
  alert_policies = { for policy in var.alert_policies : policy.display_name => policy }
  
  # Process uptime check configurations
  uptime_checks = { for check in var.uptime_checks : check.display_name => check }
  
  # Process SLO configurations
  slos = { for slo in var.slos : "${slo.service_name}-${slo.slo_id}" => slo }
  
  # Process notification channel configurations
  notification_channels = { for channel in var.notification_channels : channel.display_name => channel }
}

# Enable required APIs
resource "google_project_service" "monitoring_api" {
  project = var.project_id
  service = "monitoring.googleapis.com"
  
  disable_dependent_services = false
  disable_on_destroy         = false
}

# Create dashboards
module "dashboard" {
  source   = "./dashboard"
  for_each = local.dashboards
  
  project_id    = var.project_id
  display_name  = each.key
  layout_type   = lookup(each.value, "layout_type", "grid")
  widgets       = lookup(each.value, "widgets", [])
  
  depends_on = [google_project_service.monitoring_api]
}

# Create notification channels
resource "google_monitoring_notification_channel" "notification_channel" {
  for_each = local.notification_channels
  
  project      = var.project_id
  display_name = each.key
  type         = each.value.type
  labels       = lookup(each.value, "labels", {})
  description  = lookup(each.value, "description", null)
  
  depends_on = [google_project_service.monitoring_api]
}

# Create alert policies
module "alert_policy" {
  source   = "./alert-policy"
  for_each = local.alert_policies
  
  project_id            = var.project_id
  display_name          = each.key
  combiner              = lookup(each.value, "combiner", "OR")
  conditions            = each.value.conditions
  notification_channels = [
    for channel in lookup(each.value, "notification_channels", []) :
    google_monitoring_notification_channel.notification_channel[channel].id
  ]
  documentation         = lookup(each.value, "documentation", null)
  enabled               = lookup(each.value, "enabled", true)
  
  depends_on = [
    google_project_service.monitoring_api,
    google_monitoring_notification_channel.notification_channel
  ]
}

# Create uptime checks
module "uptime_check" {
  source   = "./uptime-check"
  for_each = local.uptime_checks
  
  project_id          = var.project_id
  display_name        = each.key
  resource_type       = each.value.resource_type
  resource_attributes = lookup(each.value, "resource_attributes", {})
  
  # HTTP check configuration
  http_check          = lookup(each.value, "http_check", null)
  
  # TCP check configuration
  tcp_check           = lookup(each.value, "tcp_check", null)
  
  # Check frequency
  period              = lookup(each.value, "period", "60s")
  timeout             = lookup(each.value, "timeout", "10s")
  
  # Content matching
  content_matchers    = lookup(each.value, "content_matchers", [])
  
  # Alert configuration
  selected_regions    = lookup(each.value, "selected_regions", ["us-central1"])
  
  depends_on = [google_project_service.monitoring_api]
}

# Create SLOs
module "slo" {
  source   = "./slo"
  for_each = local.slos
  
  project_id          = var.project_id
  service_name        = each.value.service_name
  slo_id              = each.value.slo_id
  display_name        = lookup(each.value, "display_name", each.value.slo_id)
  goal                = each.value.goal
  rolling_period_days = lookup(each.value, "rolling_period_days", 30)
  slo_type            = each.value.slo_type
  
  # Availability SLO configuration
  availability_filter = lookup(each.value, "availability_filter", null)
  
  # Latency SLO configuration
  latency_filter      = lookup(each.value, "latency_filter", null)
  latency_threshold   = lookup(each.value, "latency_threshold", null)
  
  depends_on = [google_project_service.monitoring_api]
}
