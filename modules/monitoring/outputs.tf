/**
 * Copyright 2023 Matiks
 *
 * Outputs for the Monitoring module.
 */

output "dashboards" {
  description = "The created dashboards"
  value       = module.dashboard
}

output "alert_policies" {
  description = "The created alert policies"
  value       = module.alert_policy
}

output "uptime_checks" {
  description = "The created uptime checks"
  value       = module.uptime_check
}

output "slos" {
  description = "The created SLOs"
  value       = module.slo
}

output "notification_channels" {
  description = "The created notification channels"
  value = {
    for name, channel in google_monitoring_notification_channel.notification_channel : name => {
      id           = channel.id
      name         = channel.name
      type         = channel.type
      display_name = channel.display_name
    }
  }
}
