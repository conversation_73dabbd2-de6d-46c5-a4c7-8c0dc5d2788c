/**
 * Copyright 2023 Matiks
 *
 * Outputs for the Dashboard submodule.
 */

output "dashboard_id" {
  description = "The ID of the dashboard"
  value       = google_monitoring_dashboard.dashboard.id
}

output "dashboard_name" {
  description = "The name of the dashboard"
  value       = google_monitoring_dashboard.dashboard.dashboard_json
}

output "dashboard_url" {
  description = "The URL of the dashboard"
  value       = "https://console.cloud.google.com/monitoring/dashboards/custom/${element(split("/", google_monitoring_dashboard.dashboard.id), length(split("/", google_monitoring_dashboard.dashboard.id)) - 1)}"
}
