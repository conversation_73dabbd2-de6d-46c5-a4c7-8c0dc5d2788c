# variables.tf for monitoring module

variable "metrics_scope_project_id" {
  description = "The project ID of the central monitoring project whose metrics scope will be used."
  type        = string
}

variable "monitored_project_ids" {
  description = "A list or set of project IDs to link to the central metrics scope."
  type        = set(string) # Using set to automatically handle duplicates
  default     = []
}