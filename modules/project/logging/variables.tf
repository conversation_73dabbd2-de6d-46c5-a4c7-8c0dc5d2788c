/**
 * Copyright 2023 Matiks
 *
 * Variables for the Logging Project submodule.
 */

variable "name" {
  description = "The name of the project"
  type        = string
}

variable "project_id" {
  description = "The ID of the project"
  type        = string
}

variable "org_id" {
  description = "The organization ID"
  type        = string
}

variable "folder_id" {
  description = "The folder ID where the project will be created"
  type        = string
}

variable "billing_account" {
  description = "The billing account ID"
  type        = string
}

variable "activate_apis" {
  description = "List of additional APIs to enable (beyond the default logging project APIs)"
  type        = list(string)
  default     = []
}

variable "labels" {
  description = "A map of labels to apply to the project"
  type        = map(string)
  default     = {}
}

variable "budget_amount" {
  description = "The amount to use for the budget"
  type        = number
  default     = null
}

variable "budget_alert_spent_percents" {
  description = "The list of percentages of the budget to alert on"
  type        = list(number)
  default     = [0.5, 0.7, 0.9, 1.0]
}

variable "budget_alert_pubsub_topic" {
  description = "The Pub/Sub topic to send budget alerts to"
  type        = string
  default     = null
}

variable "budget_monitoring_notification_channels" {
  description = "The monitoring notification channels to send budget alerts to"
  type        = list(string)
  default     = []
}

variable "auto_create_network" {
  description = "Whether to create the default network"
  type        = bool
  default     = false
}

variable "default_service_account" {
  description = "Default service account setting: KEEP, DEPRIVILEGE, DELETE, DISABLE"
  type        = string
  default     = "DEPRIVILEGE"
}

variable "create_project_sa" {
  description = "Whether to create a service account for the project"
  type        = bool
  default     = false
}

variable "sa_role" {
  description = "The role to give the created service account"
  type        = string
  default     = ""
}

variable "lien" {
  description = "Whether to add a lien on the project"
  type        = bool
  default     = false
}

variable "disable_services_on_destroy" {
  description = "Whether to disable services on destroy"
  type        = bool
  default     = true
}
