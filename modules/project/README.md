# Project Module

This module manages Google Cloud Platform projects, providing a structured approach to creating and managing different types of projects such as host projects, service projects, and specialized projects like logging and monitoring.

## Features

- Standardized project creation with consistent naming and labeling
- Support for different project types (host, service, logging, etc.)
- Integration with folder structure
- Automatic API enablement based on project type
- IAM role assignment
- Shared VPC attachment for service projects
- Budget and quota management

## Usage

```hcl
module "project" {
  source = "../../modules/project"

  # Required variables
  name       = "example-project"
  project_id = "example-project-id"
  org_id     = "************"
  folder_id  = "folders/************"
  
  # Optional variables
  billing_account = "ABCDEF-123456-GHIJKL"
  project_type    = "service"  # Options: "base", "host", "service", "logging"
  environment     = "prod"     # Options: "prod", "nonprod", "dev"
  
  # Service project specific variables (only needed for service projects)
  host_project_id = "vpc-host-prod-id"
  
  # API enablement
  activate_apis = [
    "compute.googleapis.com",
    "container.googleapis.com",
    "serviceusage.googleapis.com"
  ]
  
  # Labels
  labels = {
    environment = "production"
    application = "example"
  }
}
```

## Submodules

This module includes the following submodules:

- **Base**: Creates a basic GCP project with standard configurations
- **Host**: Creates a Shared VPC host project
- **Service**: Creates a service project attached to a Shared VPC host project
- **Logging**: Creates a specialized project for centralized logging and monitoring

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.3.0 |
| google | >= 4.80.0 |
| google-beta | >= 4.80.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| name | The name of the project | `string` | n/a | yes |
| project_id | The ID of the project | `string` | n/a | yes |
| org_id | The organization ID | `string` | n/a | yes |
| folder_id | The folder ID where the project will be created | `string` | n/a | yes |
| billing_account | The billing account ID | `string` | n/a | yes |
| project_type | The type of project to create (base, host, service, logging) | `string` | `"base"` | no |
| environment | The environment (prod, nonprod, dev) | `string` | `"prod"` | no |
| host_project_id | The ID of the host project (required for service projects) | `string` | `null` | no |
| activate_apis | List of APIs to enable | `list(string)` | `[]` | no |
| labels | A map of labels to apply to the project | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| project_id | The ID of the created project |
| project_number | The number of the created project |
| project_name | The name of the created project |
| service_account | The default service account for the project |
| enabled_apis | The list of enabled APIs for the project |

## Examples

- [Basic Project](../../examples/project/basic)
- [Host Project](../../examples/project/host)
- [Service Project](../../examples/project/service)
- [Logging Project](../../examples/project/logging)

## Notes

- Project IDs must be globally unique
- The module uses the Google Project Factory module under the hood
- Service projects require a host project to be specified
- The module automatically applies standard labels based on the project type and environment
