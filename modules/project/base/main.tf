/**
 * # Base Project Submodule
 *
 * This submodule creates a basic Google Cloud Platform project.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = ">= 4.80.0"
    }
  }
}

# Create the project
module "project" {
  source  = "terraform-google-modules/project-factory/google"
  version = "~> 18.0"

  name                        = var.name
  project_id                  = var.project_id
  org_id                      = var.org_id
  folder_id                   = var.folder_id
  billing_account             = var.billing_account
  activate_apis               = var.activate_apis
  labels                      = var.labels
  auto_create_network         = var.auto_create_network
  default_service_account     = var.default_service_account
  create_project_sa           = var.create_project_sa
  sa_role                     = var.sa_role
  lien                        = var.lien
  disable_services_on_destroy = var.disable_services_on_destroy
  
  # Budget configuration
  budget_amount                   = var.budget_amount
  budget_alert_spent_percents     = var.budget_alert_spent_percents
  budget_alert_pubsub_topic       = var.budget_alert_pubsub_topic
  budget_monitoring_notification_channels = var.budget_monitoring_notification_channels
}
