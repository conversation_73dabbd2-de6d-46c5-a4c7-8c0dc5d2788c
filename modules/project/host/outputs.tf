/**
 * Copyright 2023 Matiks
 *
 * Outputs for the Host Project submodule.
 */

output "project_id" {
  description = "The ID of the created project"
  value       = module.project.project_id
}

output "project_number" {
  description = "The number of the created project"
  value       = module.project.project_number
}

output "project_name" {
  description = "The name of the created project"
  value       = module.project.project_name
}

output "service_account" {
  description = "The default service account for the project"
  value       = module.project.service_account_email
}

output "service_account_id" {
  description = "The ID of the default service account for the project"
  value       = module.project.service_account_id
}

output "service_account_display_name" {
  description = "The display name of the default service account for the project"
  value       = module.project.service_account_display_name
}

output "enabled_apis" {
  description = "The list of enabled APIs for the project"
  value       = module.project.enabled_apis
}

output "budget" {
  description = "The budget resource for the project"
  value       = module.project.budget
}

output "project" {
  description = "The full project resource"
  value       = module.project
}
