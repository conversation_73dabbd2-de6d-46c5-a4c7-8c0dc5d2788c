/**
 * # Service Project Submodule
 *
 * This submodule creates a Google Cloud Platform service project attached to a Shared VPC host project.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = ">= 4.80.0"
    }
  }
}

# Default APIs for service projects
locals {
  default_apis = [
    "compute.googleapis.com",
    "container.googleapis.com",
    "serviceusage.googleapis.com"
  ]
  
  # Combine default APIs with user-provided APIs
  apis_to_enable = distinct(concat(local.default_apis, var.activate_apis))
}

# Create the service project
module "project" {
  source  = "terraform-google-modules/project-factory/google//modules/svpc_service_project"
  version = "~> 18.0"

  name                        = var.name
  project_id                  = var.project_id
  org_id                      = var.org_id
  folder_id                   = var.folder_id
  billing_account             = var.billing_account
  activate_apis               = local.apis_to_enable
  labels                      = var.labels
  auto_create_network         = var.auto_create_network
  default_service_account     = var.default_service_account
  create_project_sa           = var.create_project_sa
  sa_role                     = var.sa_role
  lien                        = var.lien
  disable_services_on_destroy = var.disable_services_on_destroy
  
  # Shared VPC configuration
  shared_vpc         = var.host_project_id
  shared_vpc_subnets = var.shared_vpc_subnets
  
  # Grant IAM roles on the host project
  grant_network_role = true
  
  # Budget configuration
  budget_amount                   = var.budget_amount
  budget_alert_spent_percents     = var.budget_alert_spent_percents
  budget_alert_pubsub_topic       = var.budget_alert_pubsub_topic
  budget_monitoring_notification_channels = var.budget_monitoring_notification_channels
}
