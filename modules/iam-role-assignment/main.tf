# ---------------------------------------------------------------------------------------------------------------------
# IAM ROLE ASSIGNMENT MODULE
# This module creates IAM role assignments for various resource types.
# ---------------------------------------------------------------------------------------------------------------------

terraform {
  required_version = ">= 1.3"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.33"
    }
  }
}

# ---------------------------------------------------------------------------------------------------------------------
# PROJECT-LEVEL IAM ROLE ASSIGNMENTS
# ---------------------------------------------------------------------------------------------------------------------

# Assign IAM roles to members at the project level
resource "google_project_iam_member" "project_iam_member" {
  for_each = var.project_role_members

  project = var.project_id
  role    = each.key
  member  = each.value
}

# Assign IAM roles to multiple members at the project level
resource "google_project_iam_member" "project_iam_members" {
  for_each = {
    for pair in var.project_role_members_list :
    "${pair.role}|${pair.member}" => pair
  }

  project = var.project_id
  role    = split("|", each.key)[0]
  member  = split("|", each.key)[1]
}

# ---------------------------------------------------------------------------------------------------------------------
# SERVICE ACCOUNT-LEVEL IAM ROLE ASSIGNMENTS
# ---------------------------------------------------------------------------------------------------------------------

# Assign IAM roles to members for a service account
resource "google_service_account_iam_member" "service_account_iam_member" {
  for_each = var.service_account_role_members

  service_account_id = var.service_account_id
  role               = each.key
  member             = each.value
}

# ---------------------------------------------------------------------------------------------------------------------
# STORAGE BUCKET-LEVEL IAM ROLE ASSIGNMENTS
# ---------------------------------------------------------------------------------------------------------------------

# Assign IAM roles to members for a storage bucket
resource "google_storage_bucket_iam_member" "bucket_iam_member" {
  for_each = var.bucket_role_members

  bucket = var.bucket_name
  role   = each.key
  member = each.value
}

# ---------------------------------------------------------------------------------------------------------------------
# SECRET MANAGER-LEVEL IAM ROLE ASSIGNMENTS
# ---------------------------------------------------------------------------------------------------------------------

# Assign IAM roles to members for a secret
resource "google_secret_manager_secret_iam_member" "secret_iam_member" {
  for_each = var.secret_role_members

  project   = var.project_id
  secret_id = var.secret_id
  role      = each.key
  member    = each.value
}

# ---------------------------------------------------------------------------------------------------------------------
# COMPUTE SUBNETWORK-LEVEL IAM ROLE ASSIGNMENTS
# ---------------------------------------------------------------------------------------------------------------------

# Assign IAM roles to members for a subnetwork
resource "google_compute_subnetwork_iam_member" "subnetwork_iam_member" {
  for_each = var.subnetwork_role_members

  project    = var.project_id
  region     = var.region
  subnetwork = var.subnetwork_name
  role       = each.key
  member     = each.value
}

# ---------------------------------------------------------------------------------------------------------------------
# COMMON IAM ROLE ASSIGNMENTS
# ---------------------------------------------------------------------------------------------------------------------

# Assign common IAM roles to members based on predefined patterns
resource "google_project_iam_member" "common_iam_assignments" {
  for_each = {
    for pair in local.common_role_assignments :
    "${pair.role}|${pair.member}" => pair
  }

  project = var.project_id
  role    = split("|", each.key)[0]
  member  = split("|", each.key)[1]
}

# ---------------------------------------------------------------------------------------------------------------------
# LOCALS
# ---------------------------------------------------------------------------------------------------------------------

locals {
  # Common role assignments based on predefined patterns
  common_role_assignments = var.use_common_roles ? [
    # GKE service account roles
    for member in var.gke_service_accounts : {
      role   = "roles/logging.logWriter"
      member = "serviceAccount:${member}"
    },
    for member in var.gke_service_accounts : {
      role   = "roles/monitoring.metricWriter"
      member = "serviceAccount:${member}"
    },
    for member in var.gke_service_accounts : {
      role   = "roles/monitoring.viewer"
      member = "serviceAccount:${member}"
    },
    for member in var.gke_service_accounts : {
      role   = "roles/stackdriver.resourceMetadata.writer"
      member = "serviceAccount:${member}"
    },
    for member in var.gke_service_accounts : {
      role   = "roles/artifactregistry.reader"
      member = "serviceAccount:${member}"
    },

    # Secret accessor roles
    for pair in var.secret_accessor_members : {
      role   = "roles/secretmanager.secretAccessor"
      member = pair
    },

    # Network user roles
    for pair in var.network_user_members : {
      role   = "roles/compute.networkUser"
      member = pair
    },

    # Viewer roles
    for pair in var.viewer_members : {
      role   = "roles/viewer"
      member = pair
    }
  ] : []
}