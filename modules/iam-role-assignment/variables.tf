# ---------------------------------------------------------------------------------------------------------------------
# VARIABLES
# Variables for the iam-role-assignment module
# ---------------------------------------------------------------------------------------------------------------------

# ---------------------------------------------------------------------------------------------------------------------
# REQUIRED VARIABLES
# ---------------------------------------------------------------------------------------------------------------------

variable "project_id" {
  description = "The ID of the project where the IAM roles will be assigned"
  type        = string
}

# ---------------------------------------------------------------------------------------------------------------------
# OPTIONAL VARIABLES
# ---------------------------------------------------------------------------------------------------------------------

# Project-level IAM role assignments
variable "project_role_members" {
  description = "Map of roles to members for project-level IAM assignments (role => member)"
  type        = map(string)
  default     = {}
}

variable "project_role_members_list" {
  description = "List of role-member pairs for project-level IAM assignments"
  type = list(object({
    role   = string
    member = string
  }))
  default = []
}

# Service account-level IAM role assignments
variable "service_account_id" {
  description = "The ID of the service account for IAM role assignments"
  type        = string
  default     = null
}

variable "service_account_role_members" {
  description = "Map of roles to members for service account-level IAM assignments (role => member)"
  type        = map(string)
  default     = {}
}

# Storage bucket-level IAM role assignments
variable "bucket_name" {
  description = "The name of the storage bucket for IAM role assignments"
  type        = string
  default     = null
}

variable "bucket_role_members" {
  description = "Map of roles to members for bucket-level IAM assignments (role => member)"
  type        = map(string)
  default     = {}
}

# Secret Manager-level IAM role assignments
variable "secret_id" {
  description = "The ID of the secret for IAM role assignments"
  type        = string
  default     = null
}

variable "secret_role_members" {
  description = "Map of roles to members for secret-level IAM assignments (role => member)"
  type        = map(string)
  default     = {}
}

# Compute subnetwork-level IAM role assignments
variable "region" {
  description = "The region of the subnetwork for IAM role assignments"
  type        = string
  default     = null
}

variable "subnetwork_name" {
  description = "The name of the subnetwork for IAM role assignments"
  type        = string
  default     = null
}

variable "subnetwork_role_members" {
  description = "Map of roles to members for subnetwork-level IAM assignments (role => member)"
  type        = map(string)
  default     = {}
}

# Common IAM role assignments
variable "use_common_roles" {
  description = "Whether to use common role assignments"
  type        = bool
  default     = false
}

variable "gke_service_accounts" {
  description = "List of GKE service account emails to assign common GKE roles"
  type        = list(string)
  default     = []
}

variable "secret_accessor_members" {
  description = "List of members to assign the Secret Manager Secret Accessor role"
  type        = list(string)
  default     = []
}

variable "network_user_members" {
  description = "List of members to assign the Compute Network User role"
  type        = list(string)
  default     = []
}

variable "viewer_members" {
  description = "List of members to assign the Viewer role"
  type        = list(string)
  default     = []
}