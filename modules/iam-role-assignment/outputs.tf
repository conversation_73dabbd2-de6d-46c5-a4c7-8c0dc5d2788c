# ---------------------------------------------------------------------------------------------------------------------
# OUTPUTS
# Outputs for the iam-role-assignment module
# ---------------------------------------------------------------------------------------------------------------------

# Project-level IAM role assignments
output "project_iam_members" {
  description = "The project IAM member resources"
  value       = google_project_iam_member.project_iam_member
}

output "project_iam_members_list" {
  description = "The project IAM member resources from the list input"
  value       = google_project_iam_member.project_iam_members
}

# Service account-level IAM role assignments
output "service_account_iam_members" {
  description = "The service account IAM member resources"
  value       = google_service_account_iam_member.service_account_iam_member
}

# Storage bucket-level IAM role assignments
output "bucket_iam_members" {
  description = "The bucket IAM member resources"
  value       = google_storage_bucket_iam_member.bucket_iam_member
}

# Secret Manager-level IAM role assignments
output "secret_iam_members" {
  description = "The secret IAM member resources"
  value       = google_secret_manager_secret_iam_member.secret_iam_member
}

# Compute subnetwork-level IAM role assignments
output "subnetwork_iam_members" {
  description = "The subnetwork IAM member resources"
  value       = google_compute_subnetwork_iam_member.subnetwork_iam_member
}

# Common IAM role assignments
output "common_iam_assignments" {
  description = "The common IAM role assignments"
  value       = google_project_iam_member.common_iam_assignments
}

# Summary of all IAM role assignments
output "all_iam_assignments" {
  description = "Summary of all IAM role assignments made by this module"
  value = {
    project_level = {
      count = length(google_project_iam_member.project_iam_member) + length(google_project_iam_member.project_iam_members)
      roles = distinct(concat(
        [for k, v in google_project_iam_member.project_iam_member : k],
        [for k, v in google_project_iam_member.project_iam_members : split("|", k)[0]]
      ))
    }
    service_account_level = {
      count = length(google_service_account_iam_member.service_account_iam_member)
      roles = [for k, v in google_service_account_iam_member.service_account_iam_member : k]
    }
    bucket_level = {
      count = length(google_storage_bucket_iam_member.bucket_iam_member)
      roles = [for k, v in google_storage_bucket_iam_member.bucket_iam_member : k]
    }
    secret_level = {
      count = length(google_secret_manager_secret_iam_member.secret_iam_member)
      roles = [for k, v in google_secret_manager_secret_iam_member.secret_iam_member : k]
    }
    subnetwork_level = {
      count = length(google_compute_subnetwork_iam_member.subnetwork_iam_member)
      roles = [for k, v in google_compute_subnetwork_iam_member.subnetwork_iam_member : k]
    }
    common_assignments = {
      count = length(google_project_iam_member.common_iam_assignments)
      roles = distinct([for k, v in google_project_iam_member.common_iam_assignments : split("|", k)[0]])
    }
  }
}