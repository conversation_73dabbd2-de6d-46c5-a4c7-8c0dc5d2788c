# ---------------------------------------------------------------------------------------------------------------------
# OUTPUTS
# Outputs for the service-account module
# ---------------------------------------------------------------------------------------------------------------------

output "email" {
  description = "The email address of the service account"
  value       = google_service_account.service_account.email
}

output "name" {
  description = "The fully-qualified name of the service account"
  value       = google_service_account.service_account.name
}

output "id" {
  description = "The ID of the service account"
  value       = google_service_account.service_account.id
}

output "unique_id" {
  description = "The unique ID of the service account"
  value       = google_service_account.service_account.unique_id
}

output "member" {
  description = "The member string for the service account, suitable for use in IAM bindings"
  value       = "serviceAccount:${google_service_account.service_account.email}"
}