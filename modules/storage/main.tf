/**
 * # Storage Module
 *
 * This module manages Google Cloud Platform storage resources, providing a structured approach to creating and managing Cloud Storage buckets, BigQuery datasets, and Cloud SQL instances.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = ">= 4.80.0"
    }
  }
}

# Local variables
locals {
  # Process bucket configurations
  buckets = { for bucket in var.buckets : bucket.name => bucket }
  
  # Process BigQuery dataset configurations
  bigquery_datasets = { for dataset in var.bigquery_datasets : dataset.dataset_id => dataset }
  
  # Process Cloud SQL instance configurations
  cloudsql_instances = { for instance in var.cloudsql_instances : instance.name => instance }
  
  # Standard labels
  standard_labels = {
    "managed-by" = "terraform"
    "module"     = "storage"
  }
}

# Enable required APIs
resource "google_project_service" "storage_api" {
  count = length(var.buckets) > 0 ? 1 : 0
  
  project = var.project_id
  service = "storage.googleapis.com"
  
  disable_dependent_services = false
  disable_on_destroy         = false
}

resource "google_project_service" "bigquery_api" {
  count = length(var.bigquery_datasets) > 0 ? 1 : 0
  
  project = var.project_id
  service = "bigquery.googleapis.com"
  
  disable_dependent_services = false
  disable_on_destroy         = false
}

resource "google_project_service" "sqladmin_api" {
  count = length(var.cloudsql_instances) > 0 ? 1 : 0
  
  project = var.project_id
  service = "sqladmin.googleapis.com"
  
  disable_dependent_services = false
  disable_on_destroy         = false
}

# Create Cloud Storage buckets
module "bucket" {
  source   = "./bucket"
  for_each = local.buckets
  
  project_id    = var.project_id
  name          = each.key
  location      = lookup(each.value, "location", "US")
  storage_class = lookup(each.value, "storage_class", "STANDARD")
  
  # Optional configurations
  force_destroy               = lookup(each.value, "force_destroy", false)
  uniform_bucket_level_access = lookup(each.value, "uniform_bucket_level_access", true)
  versioning                  = lookup(each.value, "versioning", false)
  public_access_prevention    = lookup(each.value, "public_access_prevention", "enforced")
  
  # Lifecycle rules
  lifecycle_rules = lookup(each.value, "lifecycle_rules", [])
  
  # Encryption
  encryption = lookup(each.value, "encryption", null)
  
  # Retention policy
  retention_policy = lookup(each.value, "retention_policy", null)
  
  # CORS configuration
  cors = lookup(each.value, "cors", [])
  
  # Website configuration
  website = lookup(each.value, "website", null)
  
  # Logging configuration
  logging = lookup(each.value, "logging", null)
  
  # Labels
  labels = merge(
    local.standard_labels,
    lookup(each.value, "labels", {})
  )
  
  depends_on = [google_project_service.storage_api]
}

# Create BigQuery datasets
module "bigquery" {
  source   = "./bigquery"
  for_each = local.bigquery_datasets
  
  project_id    = var.project_id
  dataset_id    = each.key
  friendly_name = lookup(each.value, "friendly_name", each.key)
  description   = lookup(each.value, "description", null)
  location      = lookup(each.value, "location", "US")
  
  # Optional configurations
  delete_contents_on_destroy      = lookup(each.value, "delete_contents_on_destroy", false)
  default_table_expiration_ms     = lookup(each.value, "default_table_expiration_ms", null)
  default_partition_expiration_ms = lookup(each.value, "default_partition_expiration_ms", null)
  
  # Access controls
  access = lookup(each.value, "access", [])
  
  # Tables
  tables = lookup(each.value, "tables", [])
  
  # Encryption
  encryption_configuration = lookup(each.value, "encryption_configuration", null)
  
  # Labels
  labels = merge(
    local.standard_labels,
    lookup(each.value, "labels", {})
  )
  
  depends_on = [google_project_service.bigquery_api]
}

# Create Cloud SQL instances
module "cloudsql" {
  source   = "./cloudsql"
  for_each = local.cloudsql_instances
  
  project_id        = var.project_id
  name              = each.key
  database_version  = lookup(each.value, "database_version", "POSTGRES_14")
  region            = lookup(each.value, "region", "us-central1")
  tier              = lookup(each.value, "tier", "db-f1-micro")
  
  # Optional configurations
  deletion_protection = lookup(each.value, "deletion_protection", true)
  
  # Settings
  settings = lookup(each.value, "settings", {})
  
  # Databases
  databases = lookup(each.value, "databases", [])
  
  # Users
  users = lookup(each.value, "users", [])
  
  # Labels
  labels = merge(
    local.standard_labels,
    lookup(each.value, "labels", {})
  )
  
  depends_on = [google_project_service.sqladmin_api]
}
