/**
 * Copyright 2023 Matiks
 *
 * Variables for the BigQuery submodule.
 */

variable "project_id" {
  description = "The ID of the project where the dataset will be created"
  type        = string
}

variable "dataset_id" {
  description = "The ID of the dataset"
  type        = string
}

variable "friendly_name" {
  description = "The friendly name of the dataset"
  type        = string
  default     = null
}

variable "description" {
  description = "The description of the dataset"
  type        = string
  default     = null
}

variable "location" {
  description = "The location of the dataset"
  type        = string
  default     = "US"
}

variable "delete_contents_on_destroy" {
  description = "Whether to delete the contents of the dataset when destroying it"
  type        = bool
  default     = false
}

variable "default_table_expiration_ms" {
  description = "The default expiration time for tables in the dataset"
  type        = number
  default     = null
}

variable "default_partition_expiration_ms" {
  description = "The default expiration time for partitions in the dataset"
  type        = number
  default     = null
}

variable "access" {
  description = "Access controls for the dataset"
  type = list(object({
    role          = string
    domain        = optional(string)
    group_by_email = optional(string)
    user_by_email = optional(string)
    special_group = optional(string)
    view = optional(object({
      project_id = string
      dataset_id = string
      table_id   = string
    }))
  }))
  default = []
}

variable "tables" {
  description = "List of tables to create in the dataset"
  type = list(object({
    table_id   = string
    schema     = optional(string)
    clustering = optional(list(string))
    expiration_time = optional(number)
    time_partitioning = optional(object({
      type                     = string
      field                    = optional(string)
      require_partition_filter = optional(bool, false)
      expiration_ms            = optional(number)
    }))
    range_partitioning = optional(object({
      field = string
      range = object({
        start    = number
        end      = number
        interval = number
      })
    }))
    view = optional(object({
      query          = string
      use_legacy_sql = optional(bool, false)
    }))
    materialized_view = optional(object({
      query          = string
      enable_refresh = optional(bool, true)
      refresh_interval_ms = optional(number)
    }))
  }))
  default = []
}

variable "encryption_configuration" {
  description = "Encryption configuration for the dataset"
  type = object({
    kms_key_name = string
  })
  default = null
}

variable "labels" {
  description = "A map of labels to apply to the dataset"
  type        = map(string)
  default     = {}
}
