/**
 * Copyright 2023 Matiks
 *
 * Outputs for the BigQuery submodule.
 */

output "dataset_id" {
  description = "The ID of the dataset"
  value       = google_bigquery_dataset.dataset.dataset_id
}

output "self_link" {
  description = "The self-link of the dataset"
  value       = google_bigquery_dataset.dataset.self_link
}

output "tables" {
  description = "The created tables"
  value = {
    for table_id, table in google_bigquery_table.table : table_id => {
      id         = table.id
      self_link  = table.self_link
      table_id   = table.table_id
      dataset_id = table.dataset_id
    }
  }
}

output "table_ids" {
  description = "The IDs of the created tables"
  value       = [for table in google_bigquery_table.table : table.id]
}

output "location" {
  description = "The location of the dataset"
  value       = google_bigquery_dataset.dataset.location
}

output "last_modified_time" {
  description = "The last modified time of the dataset"
  value       = google_bigquery_dataset.dataset.last_modified_time
}

output "creation_time" {
  description = "The creation time of the dataset"
  value       = google_bigquery_dataset.dataset.creation_time
}
