/**
 * Copyright 2023 Matiks
 *
 * Outputs for the Cloud SQL submodule.
 */

output "name" {
  description = "The name of the instance"
  value       = google_sql_database_instance.instance.name
}

output "self_link" {
  description = "The self-link of the instance"
  value       = google_sql_database_instance.instance.self_link
}

output "connection_name" {
  description = "The connection name of the instance"
  value       = google_sql_database_instance.instance.connection_name
}

output "public_ip_address" {
  description = "The public IP address of the instance"
  value       = google_sql_database_instance.instance.public_ip_address
}

output "private_ip_address" {
  description = "The private IP address of the instance"
  value       = google_sql_database_instance.instance.private_ip_address
}

output "databases" {
  description = "The created databases"
  value = {
    for db_name, db in google_sql_database.database : db_name => {
      id        = db.id
      name      = db.name
      self_link = db.self_link
    }
  }
}

output "users" {
  description = "The created users"
  value = {
    for user_name, user in google_sql_user.user : user_name => {
      id   = user.id
      name = user.name
      host = user.host
    }
  }
}
