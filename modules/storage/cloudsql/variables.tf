/**
 * Copyright 2023 Matiks
 *
 * Variables for the Cloud SQL submodule.
 */

variable "project_id" {
  description = "The ID of the project where the instance will be created"
  type        = string
}

variable "name" {
  description = "The name of the instance"
  type        = string
}

variable "database_version" {
  description = "The database version to use"
  type        = string
  default     = "POSTGRES_14"
}

variable "region" {
  description = "The region of the instance"
  type        = string
  default     = "us-central1"
}

variable "tier" {
  description = "The tier of the instance"
  type        = string
  default     = "db-f1-micro"
}

variable "deletion_protection" {
  description = "Whether to enable deletion protection on the instance"
  type        = bool
  default     = true
}

variable "settings" {
  description = "The settings of the instance"
  type        = any
  default     = {}
}

variable "databases" {
  description = "List of databases to create in the instance"
  type = list(object({
    name      = string
    charset   = optional(string)
    collation = optional(string)
  }))
  default = []
}

variable "users" {
  description = "List of users to create in the instance"
  type = list(object({
    name     = string
    password = string
    host     = optional(string)
  }))
  default = []
}

variable "labels" {
  description = "A map of labels to apply to the instance"
  type        = map(string)
  default     = {}
}
