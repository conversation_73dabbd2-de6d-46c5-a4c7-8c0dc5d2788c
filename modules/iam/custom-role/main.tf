/**
 * # Custom Role Submodule
 *
 * This submodule creates a Google Cloud Platform custom IAM role.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
  }
}

# Create a project-level custom role
resource "google_project_iam_custom_role" "custom_role" {
  project     = var.project_id
  role_id     = var.role_id
  title       = var.title
  description = var.description
  permissions = var.permissions
  stage       = var.stage
}
