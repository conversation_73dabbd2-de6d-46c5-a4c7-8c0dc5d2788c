/**
 * Copyright 2023 Matiks
 *
 * Variables for the Role Assignment submodule.
 */

variable "project_id" {
  description = "The ID of the project for project-level IAM bindings"
  type        = string
  default     = null
}

variable "folder_id" {
  description = "The ID of the folder for folder-level IAM bindings"
  type        = string
  default     = null
}

variable "organization_id" {
  description = "The ID of the organization for organization-level IAM bindings"
  type        = string
  default     = null
}

variable "project_role_members" {
  description = "Map of project IAM roles to members"
  type        = map(string)
  default     = {}
}

variable "project_role_members_list" {
  description = "Map of project IAM roles to list of members"
  type        = map(list(string))
  default     = {}
}

variable "folder_role_members" {
  description = "Map of folder IAM roles to members"
  type        = map(string)
  default     = {}
}

variable "folder_role_members_list" {
  description = "Map of folder IAM roles to list of members"
  type        = map(list(string))
  default     = {}
}

variable "organization_role_members" {
  description = "Map of organization IAM roles to members"
  type        = map(string)
  default     = {}
}

variable "organization_role_members_list" {
  description = "Map of organization IAM roles to list of members"
  type        = map(list(string))
  default     = {}
}

variable "service_account_id" {
  description = "The ID of the service account for service account IAM bindings"
  type        = string
  default     = null
}

variable "service_account_role_members" {
  description = "Map of service account IAM roles to members"
  type        = map(string)
  default     = {}
}

variable "bucket_name" {
  description = "The name of the bucket for bucket IAM bindings"
  type        = string
  default     = null
}

variable "bucket_role_members" {
  description = "Map of bucket IAM roles to members"
  type        = map(string)
  default     = {}
}

variable "bucket_role_members_list" {
  description = "Map of bucket IAM roles to list of members"
  type        = map(list(string))
  default     = {}
}

variable "secret_id" {
  description = "The ID of the secret for secret IAM bindings"
  type        = string
  default     = null
}

variable "secret_role_members" {
  description = "Map of secret IAM roles to members"
  type        = map(string)
  default     = {}
}

variable "secret_role_members_list" {
  description = "Map of secret IAM roles to list of members"
  type        = map(list(string))
  default     = {}
}

variable "subnetwork_name" {
  description = "The name of the subnetwork for subnetwork IAM bindings"
  type        = string
  default     = null
}

variable "region" {
  description = "The region for regional resources"
  type        = string
  default     = null
}

variable "subnetwork_role_members" {
  description = "Map of subnetwork IAM roles to members"
  type        = map(string)
  default     = {}
}

variable "subnetwork_role_members_list" {
  description = "Map of subnetwork IAM roles to list of members"
  type        = map(list(string))
  default     = {}
}

variable "use_common_roles" {
  description = "Whether to use common role patterns"
  type        = bool
  default     = false
}

variable "gke_service_accounts" {
  description = "List of GKE service accounts for common role patterns"
  type        = list(string)
  default     = []
}

variable "secret_accessor_members" {
  description = "List of members to grant secret accessor role"
  type        = list(string)
  default     = []
}

variable "network_user_members" {
  description = "List of members to grant network user role"
  type        = list(string)
  default     = []
}

variable "viewer_members" {
  description = "List of members to grant viewer role"
  type        = list(string)
  default     = []
}
