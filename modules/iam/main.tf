# main.tf for iam module
# Applies IAM bindings to folders and projects based on input maps.

# --- Folder Bindings ---
module "folder_iam_bindings" {
  source  = "terraform-google-modules/iam/google//modules/folders_iam"
  version = "~> 7.7" # Pinning to the same version used previously

  for_each = var.folder_bindings # Iterate over the map { folder_id => { role => [members] } }

  # Apply bindings to the specific folder ID from the map key
  folders = [each.key]

  # Use the bindings map provided as the value for the current folder ID
  bindings = each.value

  # Set mode to authoritative or additive as needed. Default is additive.
  # mode = "additive"
}


# --- Project Bindings ---
module "project_iam_bindings" {
  source  = "terraform-google-modules/iam/google//modules/projects_iam"
  version = "~> 7.7" # Pinning to the same version used previously

  for_each = var.project_bindings # Iterate over the map { project_id => { role => [members] } }

  # Apply bindings to the specific project ID from the map key
  projects = [each.key]

  # Use the bindings map provided as the value for the current project ID
  bindings = each.value

  # Set mode to authoritative or additive as needed. Default is additive.
  # mode = "additive"
}