# variables.tf for iam module

variable "folder_bindings" {
  description = "A map defining IAM bindings for folders. Keys are folder IDs, values are maps where keys are roles and values are lists of members (e.g., ['group:<EMAIL>', 'user:<EMAIL>'])."
  type = map(map(list(string)))
  default     = {}
  # Example:
  # {
  #   "folders/123456789" = {
  #     "roles/resourcemanager.folderViewer" = ["group:<EMAIL>"]
  #     "roles/resourcemanager.folderEditor" = ["group:<EMAIL>"]
  #   },
  #   "folders/987654321" = {
  #     "roles/logging.viewer" = ["group:<EMAIL>"]
  #   }
  # }
}

variable "project_bindings" {
  description = "A map defining IAM bindings for projects. Keys are project IDs, values are maps where keys are roles and values are lists of members."
  type = map(map(list(string)))
  default     = {}
  # Example:
  # {
  #   "my-project-id-1" = {
  #     "roles/owner" = ["user:<EMAIL>"]
  #     "roles/compute.instanceAdmin.v1" = ["group:<EMAIL>"]
  #   },
  #   "my-project-id-2" = {
  #     "roles/viewer" = ["group:<EMAIL>"]
  #   }
  # }
}

# Optional: Add variables for conditional bindings or specific modes if needed later.