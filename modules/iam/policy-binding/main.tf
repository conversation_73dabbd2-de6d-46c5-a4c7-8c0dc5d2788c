/**
 * # Policy Binding Submodule
 *
 * This submodule manages IAM policy bindings for Google Cloud Platform resources.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
  }
}

# Create project-level IAM policy bindings
resource "google_project_iam_binding" "project_iam_binding" {
  for_each = { for binding in var.policy_bindings : binding.role => binding }
  
  project = var.project_id
  role    = each.key
  members = each.value.members
}
