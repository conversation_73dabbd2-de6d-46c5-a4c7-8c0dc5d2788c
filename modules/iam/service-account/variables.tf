/**
 * Copyright 2023 Matiks
 *
 * Variables for the Service Account submodule.
 */

variable "project_id" {
  description = "The ID of the project where the service account will be created"
  type        = string
}

variable "account_id" {
  description = "The ID of the service account"
  type        = string
}

variable "display_name" {
  description = "The display name of the service account"
  type        = string
}

variable "description" {
  description = "The description of the service account"
  type        = string
  default     = null
}

variable "generate_key" {
  description = "Whether to generate a key for the service account"
  type        = bool
  default     = false
}

variable "key_algorithm" {
  description = "The algorithm used to generate the key"
  type        = string
  default     = "KEY_ALG_RSA_2048"
  validation {
    condition     = contains(["KEY_ALG_RSA_1024", "KEY_ALG_RSA_2048"], var.key_algorithm)
    error_message = "Key algorithm must be one of: KEY_ALG_RSA_1024, KEY_ALG_RSA_2048."
  }
}

variable "key_type" {
  description = "The type of the key"
  type        = string
  default     = "TYPE_GOOGLE_CREDENTIALS_FILE"
  validation {
    condition     = contains(["TYPE_GOOGLE_CREDENTIALS_FILE", "TYPE_X509_PEM_FILE"], var.key_type)
    error_message = "Key type must be one of: TYPE_GOOGLE_CREDENTIALS_FILE, TYPE_X509_PEM_FILE."
  }
}

variable "project_roles" {
  description = "List of IAM roles to assign to the service account at the project level"
  type        = list(string)
  default     = []
}

variable "workload_identity_config" {
  description = "Workload identity federation configuration"
  type = object({
    pool_id     = string
    provider_id = string
    namespace   = string
    service_account = string
  })
  default = null
}
