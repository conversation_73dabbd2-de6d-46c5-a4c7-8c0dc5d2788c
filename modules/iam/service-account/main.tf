/**
 * # Service Account Submodule
 *
 * This submodule creates a Google Cloud Platform service account and optionally generates a key.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
  }
}

# Create the service account
resource "google_service_account" "service_account" {
  project      = var.project_id
  account_id   = var.account_id
  display_name = var.display_name
  description  = var.description
}

# Generate a service account key if requested
resource "google_service_account_key" "key" {
  count = var.generate_key ? 1 : 0
  
  service_account_id = google_service_account.service_account.name
  key_algorithm      = var.key_algorithm
  public_key_type    = var.key_type
}

# Assign IAM roles to the service account at the project level
resource "google_project_iam_member" "project_roles" {
  for_each = toset(var.project_roles)
  
  project = var.project_id
  role    = each.value
  member  = "serviceAccount:${google_service_account.service_account.email}"
}

# Configure workload identity federation if requested
resource "google_service_account_iam_binding" "workload_identity_binding" {
  count = var.workload_identity_config != null ? 1 : 0
  
  service_account_id = google_service_account.service_account.name
  role               = "roles/iam.workloadIdentityUser"
  
  members = [
    "principalSet://iam.googleapis.com/projects/${var.project_id}/locations/global/workloadIdentityPools/${var.workload_identity_config.pool_id}/attribute.${var.workload_identity_config.provider_id}/${var.workload_identity_config.namespace}/${var.workload_identity_config.service_account}"
  ]
}
