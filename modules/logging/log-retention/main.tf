/**
 * # Log Retention Submodule
 *
 * This submodule configures log retention for Google Cloud Platform logs.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
  }
}

# Create a log bucket with retention configuration
resource "google_logging_project_bucket_config" "log_bucket" {
  project        = var.project_id
  location       = var.bucket_location
  bucket_id      = var.bucket_name
  retention_days = var.retention_days
  description    = var.description
}
