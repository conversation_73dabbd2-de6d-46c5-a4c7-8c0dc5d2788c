# Logging Module

This module manages Google Cloud Platform logging resources, providing a structured approach to creating and managing log exports, log sinks, log metrics, and log retention policies.

## Features

- Log export configuration for centralized logging
- Log sink creation for routing logs to various destinations (Cloud Storage, BigQuery, Pub/Sub, Log Bucket)
- Log metric creation for monitoring and alerting on log events
- Log retention policy configuration for compliance and cost optimization
- Support for organization, folder, project, and billing account level logging
- Consistent naming, labeling, and security configurations across logging resources

## Usage

```hcl
module "logging" {
  source = "../../modules/logging"

  # Required variables
  project_id = "my-project-id"
  
  # Log export configuration
  log_exports = [
    {
      name        = "all-logs-to-storage"
      description = "Export all logs to Cloud Storage"
      filter      = ""  # Empty filter means all logs
      destination = "storage"
      destination_resource = {
        name     = "my-logs-bucket"
        location = "us-central1"
      }
    },
    {
      name        = "error-logs-to-bigquery"
      description = "Export error logs to BigQuery"
      filter      = "severity >= ERROR"
      destination = "bigquery"
      destination_resource = {
        dataset_id = "logs_dataset"
        table_id   = "error_logs"
        location   = "US"
      }
    }
  ]
  
  # Log sink configuration
  log_sinks = [
    {
      name                   = "security-logs-sink"
      destination            = "logging.googleapis.com/projects/my-project-id/locations/global/buckets/security-logs"
      filter                 = "logName:\"cloudaudit.googleapis.com%\""
      unique_writer_identity = true
    }
  ]
  
  # Log metric configuration
  log_metrics = [
    {
      name        = "error-count"
      description = "Count of error logs"
      filter      = "severity >= ERROR"
      metric_descriptor = {
        metric_kind = "DELTA"
        value_type  = "INT64"
        labels = [
          {
            key         = "resource_type"
            value_type  = "STRING"
            description = "Resource type that generated the error"
          }
        ]
      }
      label_extractors = {
        "resource_type" = "REGEXP_EXTRACT(resource.type, \"(.*)\")"
      }
    }
  ]
  
  # Log retention configuration
  log_retention = {
    retention_days = 30
    bucket_name    = "my-logs-bucket"
  }
}
```

## Submodules

This module includes the following submodules:

- **Log Export**: Creates and manages log exports to various destinations
- **Log Sink**: Creates and manages log sinks for routing logs
- **Log Metric**: Creates and manages log-based metrics for monitoring
- **Log Retention**: Configures log retention policies

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.3.0 |
| google | >= 4.80.0 |
| google-beta | >= 4.80.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| project_id | The ID of the project where resources will be created | `string` | n/a | yes |
| log_exports | List of log export configurations | `list(object)` | `[]` | no |
| log_sinks | List of log sink configurations | `list(object)` | `[]` | no |
| log_metrics | List of log metric configurations | `list(object)` | `[]` | no |
| log_retention | Log retention configuration | `object` | `null` | no |

## Outputs

| Name | Description |
|------|-------------|
| log_exports | The created log exports |
| log_sinks | The created log sinks |
| log_metrics | The created log metrics |
| log_retention | The log retention configuration |

## Examples

- [Basic Logging](../../examples/logging/basic)
- [Security Logging](../../examples/logging/security)
- [Compliance Logging](../../examples/logging/compliance)
- [Multi-Destination Logging](../../examples/logging/multi-destination)

## Notes

- Consider using a centralized logging project for all logs
- Use appropriate filters to reduce log volume and costs
- Configure appropriate retention periods based on compliance requirements
- Use log-based metrics for monitoring and alerting on important events
- Consider using Log Analytics for advanced log analysis
