/**
 * # Log Export Submodule
 *
 * This submodule creates a Google Cloud Platform log export with a destination resource.
 */

terraform {
  required_version = ">= 1.3.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.80.0"
    }
  }
}

# Local variables
locals {
  # Determine destination type
  is_storage  = var.destination == "storage"
  is_bigquery = var.destination == "bigquery"
  is_pubsub   = var.destination == "pubsub"
  is_logging  = var.destination == "logging"
}

# Create a Cloud Storage bucket for log export if needed
resource "google_storage_bucket" "log_bucket" {
  count = local.is_storage ? 1 : 0
  
  project       = var.project_id
  name          = var.destination_resource.name
  location      = lookup(var.destination_resource, "location", "US")
  force_destroy = true
  
  uniform_bucket_level_access = true
  
  lifecycle_rule {
    condition {
      age = 365
    }
    action {
      type = "Delete"
    }
  }
}

# Create a BigQuery dataset for log export if needed
resource "google_bigquery_dataset" "log_dataset" {
  count = local.is_bigquery ? 1 : 0
  
  project    = var.project_id
  dataset_id = lookup(var.destination_resource, "dataset_id", var.destination_resource.name)
  location   = lookup(var.destination_resource, "location", "US")
  
  delete_contents_on_destroy = true
}

# Create a BigQuery table for log export if needed
resource "google_bigquery_table" "log_table" {
  count = local.is_bigquery && lookup(var.destination_resource, "table_id", null) != null ? 1 : 0
  
  project    = var.project_id
  dataset_id = google_bigquery_dataset.log_dataset[0].dataset_id
  table_id   = var.destination_resource.table_id
  
  deletion_protection = false
  
  schema = <<EOF
[
  {
    "name": "log_name",
    "type": "STRING",
    "mode": "NULLABLE",
    "description": "The resource name of the log"
  },
  {
    "name": "resource",
    "type": "RECORD",
    "mode": "NULLABLE",
    "description": "The monitored resource that produced this log",
    "fields": [
      {
        "name": "type",
        "type": "STRING",
        "mode": "NULLABLE",
        "description": "The resource type"
      },
      {
        "name": "labels",
        "type": "RECORD",
        "mode": "REPEATED",
        "description": "Resource labels",
        "fields": [
          {
            "name": "key",
            "type": "STRING",
            "mode": "NULLABLE",
            "description": "Label key"
          },
          {
            "name": "value",
            "type": "STRING",
            "mode": "NULLABLE",
            "description": "Label value"
          }
        ]
      }
    ]
  },
  {
    "name": "timestamp",
    "type": "TIMESTAMP",
    "mode": "NULLABLE",
    "description": "The timestamp of the log entry"
  },
  {
    "name": "severity",
    "type": "STRING",
    "mode": "NULLABLE",
    "description": "The severity of the log entry"
  },
  {
    "name": "jsonPayload",
    "type": "STRING",
    "mode": "NULLABLE",
    "description": "The JSON payload of the log entry"
  },
  {
    "name": "textPayload",
    "type": "STRING",
    "mode": "NULLABLE",
    "description": "The text payload of the log entry"
  },
  {
    "name": "insertId",
    "type": "STRING",
    "mode": "NULLABLE",
    "description": "A unique identifier for the log entry"
  },
  {
    "name": "labels",
    "type": "RECORD",
    "mode": "REPEATED",
    "description": "Log entry labels",
    "fields": [
      {
        "name": "key",
        "type": "STRING",
        "mode": "NULLABLE",
        "description": "Label key"
      },
      {
        "name": "value",
        "type": "STRING",
        "mode": "NULLABLE",
        "description": "Label value"
      }
    ]
  }
]
EOF
  
  depends_on = [google_bigquery_dataset.log_dataset]
}

# Create a Pub/Sub topic for log export if needed
resource "google_pubsub_topic" "log_topic" {
  count = local.is_pubsub ? 1 : 0
  
  project = var.project_id
  name    = lookup(var.destination_resource, "topic_id", var.destination_resource.name)
}

# Create a Log Bucket for log export if needed
resource "google_logging_project_bucket_config" "log_bucket_config" {
  count = local.is_logging ? 1 : 0
  
  project        = var.project_id
  location       = lookup(var.destination_resource, "location", "global")
  bucket_id      = var.destination_resource.name
  retention_days = lookup(var.destination_resource, "retention_days", 30)
  description    = lookup(var.destination_resource, "description", "Log bucket for ${var.name}")
}

# Create the log sink
resource "google_logging_project_sink" "log_sink" {
  project = var.project_id
  name    = var.name
  filter  = var.filter
  
  # Set the destination based on the destination type
  destination = local.is_storage ? "storage.googleapis.com/${google_storage_bucket.log_bucket[0].name}" :
                local.is_bigquery ? "bigquery.googleapis.com/projects/${var.project_id}/datasets/${google_bigquery_dataset.log_dataset[0].dataset_id}" :
                local.is_pubsub ? "pubsub.googleapis.com/projects/${var.project_id}/topics/${google_pubsub_topic.log_topic[0].name}" :
                "logging.googleapis.com/projects/${var.project_id}/locations/${google_logging_project_bucket_config.log_bucket_config[0].location}/buckets/${google_logging_project_bucket_config.log_bucket_config[0].bucket_id}"
  
  description = var.description
  
  # Use unique writer identity for proper IAM permissions
  unique_writer_identity = true
  
  # BigQuery-specific options
  dynamic "bigquery_options" {
    for_each = local.is_bigquery ? [1] : []
    content {
      use_partitioned_tables = true
    }
  }
  
  depends_on = [
    google_storage_bucket.log_bucket,
    google_bigquery_dataset.log_dataset,
    google_pubsub_topic.log_topic,
    google_logging_project_bucket_config.log_bucket_config
  ]
}

# Grant permissions to the log sink service account
resource "google_project_iam_member" "log_sink_writer_storage" {
  count = local.is_storage ? 1 : 0
  
  project = var.project_id
  role    = "roles/storage.objectCreator"
  member  = google_logging_project_sink.log_sink.writer_identity
}

resource "google_project_iam_member" "log_sink_writer_bigquery" {
  count = local.is_bigquery ? 1 : 0
  
  project = var.project_id
  role    = "roles/bigquery.dataEditor"
  member  = google_logging_project_sink.log_sink.writer_identity
}

resource "google_project_iam_member" "log_sink_writer_pubsub" {
  count = local.is_pubsub ? 1 : 0
  
  project = var.project_id
  role    = "roles/pubsub.publisher"
  member  = google_logging_project_sink.log_sink.writer_identity
}
