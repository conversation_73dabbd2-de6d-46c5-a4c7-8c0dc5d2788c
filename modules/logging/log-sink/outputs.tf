/**
 * Copyright 2023 Matiks
 *
 * Outputs for the Log Sink submodule.
 */

output "sink_name" {
  description = "The name of the log sink"
  value       = google_logging_project_sink.log_sink.name
}

output "sink_id" {
  description = "The ID of the log sink"
  value       = google_logging_project_sink.log_sink.id
}

output "writer_identity" {
  description = "The service account that was granted the permissions to write to the destination"
  value       = google_logging_project_sink.log_sink.writer_identity
}

output "destination" {
  description = "The destination of the log sink"
  value       = google_logging_project_sink.log_sink.destination
}

output "filter" {
  description = "The filter of the log sink"
  value       = google_logging_project_sink.log_sink.filter
}

output "exclusions" {
  description = "The exclusions of the log sink"
  value       = google_logging_project_sink.log_sink.exclusions
}
