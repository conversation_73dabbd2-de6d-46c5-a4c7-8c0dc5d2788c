variable "project_id" {
  description = "The project ID to host the cluster in"
  type        = string
}

variable "name" {
  description = "The name for the GKE cluster"
  type        = string
}

variable "region" {
  description = "The region for the GKE cluster"
  type        = string
}

variable "network_self_link" {
  description = "The self-link of the VPC network"
  type        = string
}

variable "subnetwork_self_link" {
  description = "The self-link of the subnetwork"
  type        = string
}

variable "pods_ip_range_name" {
  description = "The secondary IP range name for GKE pods in the subnetwork"
  type        = string
  default     = "gke-pods"
}

variable "services_ip_range_name" {
  description = "The secondary IP range name for GKE services in the subnetwork"
  type        = string
  default     = "gke-services"
}

variable "release_channel" {
  description = "The GKE release channel (STABLE, REGULAR, RAPID)"
  type        = string
  default     = "STABLE"
}

variable "description" {
  description = "An optional description for the GKE cluster"
  type        = string
  default     = "GKE Autopilot Cluster managed by Terraform"
}

variable "labels" {
  description = "A map of labels to assign to the cluster"
  type        = map(string)
  default     = {}
}
variable "node_service_account_email" {
  description = "The email address of the service account to be used by GKE nodes. If empty, the default Compute Engine service account is used."
  type        = string
  default     = null # Default to null, meaning use GCE default unless specified
}