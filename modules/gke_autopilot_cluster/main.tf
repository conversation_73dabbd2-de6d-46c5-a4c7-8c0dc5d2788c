terraform {
  required_providers {
    google = {
      source  = "hashicorp/google-beta"
      version = "~> 6.33"
    }
  }
}

resource "google_container_cluster" "autopilot_cluster" {
  project               = var.project_id
  name                  = var.name
  location              = var.region # Autopilot clusters are regional
  description           = var.description
  enable_autopilot      = true
  network               = var.network_self_link
  subnetwork            = var.subnetwork_self_link


  # Release channel configuration
  release_channel {
    channel = var.release_channel
  }

  # VPC-native settings using secondary IP ranges
  ip_allocation_policy {
    cluster_secondary_range_name  = var.pods_ip_range_name
    services_secondary_range_name = var.services_ip_range_name
  }

  # Private cluster configuration
  private_cluster_config {
    enable_private_nodes        = var.enable_private_nodes
    enable_private_endpoint     = var.enable_private_endpoint
    master_ipv4_cidr_block      = var.enable_private_nodes ? var.master_ipv4_cidr_block : null
    master_global_access_config {
      enabled = var.master_global_access
    }
  }

  # Master authorized networks
  dynamic "master_authorized_networks_config" {
    for_each = var.master_authorized_networks != null ? [1] : []
    content {
      dynamic "cidr_blocks" {
        for_each = var.master_authorized_networks
        content {
          cidr_block   = cidr_blocks.value.cidr_block
          display_name = cidr_blocks.value.display_name
        }
      }
    }
  }

  # Enhanced addons configuration
  addons_config {
    http_load_balancing {
      disabled = false
    }
  }

  # Secret Manager configuration
  secret_manager_config {
    enabled = var.enable_secret_manager
  }

  # Enable Workload Identity Federation for GKE
  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }

  # Enhanced monitoring and logging
  monitoring_config {
    enable_components = var.monitoring_components
    managed_prometheus {
      enabled = var.enable_managed_prometheus
    }

    # Advanced Prometheus configuration for application monitoring
    advanced_datapath_observability_config {
      enable_metrics = var.enable_advanced_datapath_observability
      enable_relay   = false
    }
  }

  logging_config {
    enable_components = var.logging_components
  }

  # Maintenance policy
  dynamic "maintenance_policy" {
    for_each = var.maintenance_start_time != null ? [1] : []
    content {
      daily_maintenance_window {
        start_time = var.maintenance_start_time
      }

      dynamic "maintenance_exclusion" {
        for_each = var.maintenance_exclusions
        content {
          exclusion_name = maintenance_exclusion.value.name
          start_time     = maintenance_exclusion.value.start_time
          end_time       = maintenance_exclusion.value.end_time
          exclusion_options {
            scope = maintenance_exclusion.value.scope
          }
        }
      }
    }
  }

  # Notification configuration
  dynamic "notification_config" {
    for_each = var.notification_config_topic != "" ? [1] : []
    content {
      pubsub {
        enabled = true
        topic   = var.notification_config_topic
      }
    }
  }

  # Binary Authorization
  dynamic "binary_authorization" {
    for_each = var.enable_binary_authorization ? [1] : []
    content {
      evaluation_mode = var.binary_authorization_evaluation_mode
    }
  }

  # Resource usage export
  dynamic "resource_usage_export_config" {
    for_each = var.enable_resource_usage_export && var.resource_usage_bigquery_dataset_id != "" ? [1] : []
    content {
      enable_network_egress_metering = var.enable_network_egress_export
      bigquery_destination {
        dataset_id = var.resource_usage_bigquery_dataset_id
      }
    }
  }

  # DNS configuration
  dynamic "dns_config" {
    for_each = var.cluster_dns_provider != null || var.cluster_dns_scope != null || var.cluster_dns_domain != null ? [1] : []
    content {
      cluster_dns        = var.cluster_dns_provider
      cluster_dns_scope  = var.cluster_dns_scope
      cluster_dns_domain = var.cluster_dns_domain
    }
  }

  # Confidential nodes
  dynamic "confidential_nodes" {
    for_each = var.enable_confidential_nodes ? [1] : []
    content {
      enabled = true
    }
  }

  # Cost management
  dynamic "cost_management_config" {
    for_each = var.enable_cost_allocation ? [1] : []
    content {
      enabled = true
    }
  }

  # Security posture
  dynamic "security_posture_config" {
    for_each = var.enable_security_posture ? [1] : []
    content {
      mode = "BASIC"
      vulnerability_mode = "VULNERABILITY_ENTERPRISE"
    }
  }

  # Note: Backup for GKE is managed separately through the backup-for-gke add-on
  # and not directly in the cluster resource

  # Required for Autopilot but ignored
  initial_node_count = 1

  # Labels
  resource_labels = var.labels

  # Node configuration - minimal for Autopilot as nodes are managed by GKE
  dynamic "node_config" {
    for_each = var.node_service_account_email != null ? [1] : []
    content {
      # Use custom service account if provided
      service_account = var.node_service_account_email

      # Security hardening
      metadata = {
        disable-legacy-endpoints = "true"
      }
    }
  }

  # Protect against accidental deletion
  deletion_protection = var.deletion_protection

  # Datapath provider (advanced networking)
  datapath_provider = var.datapath_provider

  # Vertical Pod Autoscaling
  vertical_pod_autoscaling {
    enabled = var.enable_vertical_pod_autoscaling
  }

  # Fleet registration (if enabled)
  dynamic "fleet" {
    for_each = var.enable_fleet ? [1] : []
    content {
      project = var.fleet_project != "" ? var.fleet_project : var.project_id
    }
  }

  # Note: Intranode visibility is managed by GKE in Autopilot mode

  # Timeouts for operations
  timeouts {
    create = "45m"
    update = "45m"
    delete = "45m"
  }

  # Prevent recreation of the cluster due to immaterial changes
  # lifecycle {
  #   ignore_changes = [
  #     # Network and subnetwork format changes after apply
  #     network,
  #     subnetwork,
  #     # Node locations can change based on GKE's decisions
  #     node_locations,
  #     # Other computed attributes
  #     master_version,
  #     node_version,
  #     # Node configuration that might change in Autopilot mode
  #     node_config,
  #     # Various configurations that might change in Autopilot mode
  #     addons_config,
  #     binary_authorization,
  #     database_encryption,
  #     default_snat_status,
  #     dns_config,
  #     ip_allocation_policy,
  #     maintenance_policy,
  #     master_auth,
  #     master_authorized_networks_config,
  #     monitoring_config,
  #     network_policy,
  #     node_pool,
  #     node_pool_auto_config,
  #     node_pool_defaults,
  #     notification_config,
  #     pod_security_policy_config,
  #     private_cluster_config,
  #     release_channel,
  #     service_external_ips_config,
  #     vertical_pod_autoscaling,
  #     workload_identity_config,
  #     # Fleet configuration
  #     fleet
  #   ]
  # }
}
