[{"name": "id", "type": "STRING", "mode": "REQUIRED", "description": "Unique identifier"}, {"name": "field1", "type": "STRING", "mode": "NULLABLE", "description": "Example field used for clustering"}, {"name": "created_at", "type": "TIMESTAMP", "mode": "REQUIRED", "description": "Creation timestamp used for partitioning"}, {"name": "value", "type": "NUMERIC", "mode": "NULLABLE", "description": "Example numeric value"}, {"name": "is_active", "type": "BOOLEAN", "mode": "NULLABLE", "description": "Whether the record is active"}, {"name": "metadata", "type": "RECORD", "mode": "NULLABLE", "description": "Additional metadata", "fields": [{"name": "source", "type": "STRING", "mode": "NULLABLE", "description": "Source of the record"}, {"name": "version", "type": "INTEGER", "mode": "NULLABLE", "description": "Version number"}]}]