/**
 * Copyright 2023 Matiks
 *
 * Outputs for the basic storage example.
 */

output "bucket_names" {
  description = "The names of the created Cloud Storage buckets"
  value       = module.storage.bucket_names
}

output "bucket_urls" {
  description = "The URLs of the created Cloud Storage buckets"
  value       = module.storage.bucket_urls
}

output "bigquery_dataset_ids" {
  description = "The IDs of the created BigQuery datasets"
  value       = module.storage.bigquery_dataset_ids
}

output "bigquery_tables" {
  description = "The created BigQuery tables"
  value       = module.storage.bigquery_tables
}

output "cloudsql_instance_names" {
  description = "The names of the created Cloud SQL instances"
  value       = module.storage.cloudsql_instance_names
}

output "cloudsql_instance_connection_names" {
  description = "The connection names of the created Cloud SQL instances"
  value       = module.storage.cloudsql_instance_connection_names
}

output "cloudsql_databases" {
  description = "The created Cloud SQL databases"
  value       = module.storage.cloudsql_databases
}

output "cloudsql_users" {
  description = "The created Cloud SQL users"
  value       = module.storage.cloudsql_users
}
