/**
 * # Basic Project Example
 *
 * This example demonstrates how to use the project module to create a basic GCP project.
 */

provider "google" {
  project = var.project_id
  region  = var.region
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
}

module "project" {
  source = "../../../modules/project"

  name            = "example-project"
  project_id      = var.project_id
  org_id          = var.org_id
  folder_id       = var.folder_id
  billing_account = var.billing_account
  
  # Project type (base, host, service, logging)
  project_type = "base"
  environment  = "dev"
  
  # APIs to enable
  activate_apis = [
    "compute.googleapis.com",
    "container.googleapis.com",
    "serviceusage.googleapis.com",
    "storage-api.googleapis.com"
  ]
  
  # Budget configuration
  budget_amount = 1000
  
  # Labels
  labels = {
    application = "example"
    team        = "infrastructure"
  }
}
