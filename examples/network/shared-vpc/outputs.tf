/**
 * Copyright 2023 Matiks
 *
 * Outputs for the shared VPC network example.
 */

output "network_id" {
  description = "The ID of the Shared VPC network"
  value       = module.shared_vpc.network_id
}

output "network_self_link" {
  description = "The URI of the Shared VPC network"
  value       = module.shared_vpc.network_self_link
}

output "subnet_ids" {
  description = "Map of subnet names to IDs"
  value       = module.shared_vpc.subnet_ids
}

output "subnet_self_links" {
  description = "Map of subnet names to self links"
  value       = module.shared_vpc.subnet_self_links
}

output "secondary_ranges" {
  description = "Map of subnet names to secondary IP ranges"
  value       = module.shared_vpc.secondary_ranges
}

output "host_project" {
  description = "The Shared VPC host project"
  value       = google_compute_shared_vpc_host_project.host.project
}

output "service_projects" {
  description = "The Shared VPC service projects"
  value       = [for sp in google_compute_shared_vpc_service_project.service_projects : sp.service_project]
}
