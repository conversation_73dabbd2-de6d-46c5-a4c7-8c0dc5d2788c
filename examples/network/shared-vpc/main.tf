/**
 * # Shared VPC Network Example
 *
 * This example demonstrates how to use the network module to create a Shared VPC network with host and service projects.
 */

provider "google" {
  project = var.host_project_id
  region  = var.region
}

provider "google-beta" {
  project = var.host_project_id
  region  = var.region
}

# Create the Shared VPC network in the host project
module "shared_vpc" {
  source = "../../../modules/network"

  project_id = var.host_project_id
  name       = "shared-vpc"
  
  # VPC configuration
  auto_create_subnetworks = false
  routing_mode            = "GLOBAL"
  mtu                     = 1460
  description             = "Shared VPC network for production and non-production environments"
  
  # Subnet configuration
  subnets = [
    {
      name          = "subnet-prod-1"
      ip_cidr_range = "10.0.0.0/24"
      region        = var.region
      description   = "Production subnet"
      private_access = true
      secondary_ip_ranges = [
        {
          range_name    = "gke-pods"
          ip_cidr_range = "********/16"
        },
        {
          range_name    = "gke-services"
          ip_cidr_range = "********/20"
        }
      ]
    },
    {
      name          = "subnet-non-prod-2"
      ip_cidr_range = "*********/24"
      region        = var.region
      description   = "Non-production subnet"
      private_access = true
      secondary_ip_ranges = [
        {
          range_name    = "gke-pods"
          ip_cidr_range = "*********/16"
        },
        {
          range_name    = "gke-services"
          ip_cidr_range = "*********/20"
        }
      ]
    }
  ]
  
  # Firewall configuration
  firewall_rules = [
    {
      name        = "vpc-allow-internal"
      description = "Allow internal traffic"
      direction   = "INGRESS"
      priority    = 1000
      source_ranges = ["10.0.0.0/8"]
      target_tags = ["internal"]
      allow = [
        {
          protocol = "tcp"
          ports    = ["0-65535"]
        },
        {
          protocol = "udp"
          ports    = ["0-65535"]
        },
        {
          protocol = "icmp"
        }
      ]
      log_config = {
        metadata = "INCLUDE_ALL_METADATA"
      }
    },
    {
      name        = "vpc-deny-all-ingress"
      description = "Deny all ingress traffic"
      direction   = "INGRESS"
      priority    = 65000
      deny = [
        {
          protocol = "all"
        }
      ]
    }
  ]
  
  # NAT configuration
  nat_config = {
    region                             = var.region
    nat_ip_allocate_option             = "AUTO_ONLY"
    source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  }
  
  # Labels
  labels = {
    environment = "shared"
    managed-by  = "terraform"
  }
}

# Enable the Shared VPC host project
resource "google_compute_shared_vpc_host_project" "host" {
  project = var.host_project_id
}

# Attach service projects to the Shared VPC host project
resource "google_compute_shared_vpc_service_project" "service_projects" {
  for_each        = toset(var.service_project_ids)
  host_project    = google_compute_shared_vpc_host_project.host.project
  service_project = each.value
}

# Grant IAM permissions for service project service accounts to use the Shared VPC
resource "google_compute_subnetwork_iam_member" "service_account_prod_subnet_user" {
  for_each   = toset(var.service_account_emails)
  project    = var.host_project_id
  region     = var.region
  subnetwork = module.shared_vpc.subnet_self_links["subnet-prod-1"]
  role       = "roles/compute.networkUser"
  member     = "serviceAccount:${each.value}"
  
  depends_on = [module.shared_vpc]
}

resource "google_compute_subnetwork_iam_member" "service_account_non_prod_subnet_user" {
  for_each   = toset(var.service_account_emails)
  project    = var.host_project_id
  region     = var.region
  subnetwork = module.shared_vpc.subnet_self_links["subnet-non-prod-2"]
  role       = "roles/compute.networkUser"
  member     = "serviceAccount:${each.value}"
  
  depends_on = [module.shared_vpc]
}
