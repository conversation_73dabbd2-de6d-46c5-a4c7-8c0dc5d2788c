# Matiks Infrastructure Disaster Recovery Plan

This document outlines the disaster recovery procedures for the Matiks infrastructure on Google Cloud Platform. It provides step-by-step instructions for recovering from various types of failures and outages.

## Overview

The Matiks infrastructure is designed with resilience and disaster recovery in mind. Key components include:

- Infrastructure as Code (IaC) using Terraform
- Versioned Terraform state in Google Cloud Storage
- Multi-region deployment for critical services
- Regular backups of application data and configurations
- Monitoring and alerting for early detection of issues

## Recovery Scenarios

### 1. Infrastructure Component Failure

#### GKE Cluster Failure

If a GKE cluster becomes unresponsive or corrupted:

1. **Assess the Failure**:
   ```bash
   gcloud container clusters describe gke-autopilot-prod-us-west1 \
     --project=matiks-prod-svc-a07 --region=us-west1
   ```

2. **Attempt Repair**:
   ```bash
   gcloud container clusters repair gke-autopilot-prod-us-west1 \
     --project=matiks-prod-svc-a07 --region=us-west1
   ```

3. **If Repair Fails, Recreate Using Terraform**:
   ```bash
   terraform apply -target=module.gke_autopilot_prod
   ```

4. **Verify Cluster Health**:
   ```bash
   kubectl get nodes
   kubectl get pods --all-namespaces
   ```

#### Network Component Failure

If VPC, subnets, or firewall rules are misconfigured:

1. **Verify Current Configuration**:
   ```bash
   gcloud compute networks describe vpc-prod-shared \
     --project=vpc-host-prod-ii997-pc158
   ```

2. **Recreate Using Terraform**:
   ```bash
   terraform apply -target=module.cs-vpc-prod-shared
   ```

### 2. Project or Service Account Issues

If projects or service accounts are deleted or corrupted:

1. **Verify Project Status**:
   ```bash
   gcloud projects describe matiks-prod-svc-a07
   ```

2. **Recreate Projects Using Terraform**:
   ```bash
   terraform apply -target=module.cs-svc-matiks-prod-svc
   ```

3. **Verify Service Accounts**:
   ```bash
   gcloud iam service-accounts list --project=matiks-prod-svc-a07
   ```

4. **Recreate Service Accounts and IAM Bindings**:
   ```bash
   terraform apply -target=google_service_account.gke_service_account_prod
   terraform apply -target=google_project_iam_member.gke_sa_prod_roles
   ```

### 3. Complete Infrastructure Recovery

In case of a catastrophic failure requiring complete infrastructure recovery:

1. **Ensure Access to Terraform State**:
   Verify access to the GCS bucket containing the Terraform state.

2. **Clone the Infrastructure Repository**:
   ```bash
   git clone https://github.com/matiks/matiks-infra.git
   cd matiks-infra
   ```

3. **Initialize Terraform**:
   ```bash
   terraform init
   ```

4. **Apply the Terraform Configuration**:
   ```bash
   terraform apply
   ```

5. **Verify Core Infrastructure**:
   - Check projects, VPCs, and subnets
   - Verify GKE clusters are running
   - Confirm IAM permissions are correctly applied

6. **Restore Application Data**:
   - Restore application data from backups
   - Redeploy applications to GKE clusters

## Backup Procedures

### Terraform State Backup

The Terraform state is stored in a versioned GCS bucket. Additional backups are created monthly:

```bash
# Create a dated backup of the Terraform state
gsutil cp gs://matiks-terraform-state/terraform.tfstate \
  gs://matiks-terraform-state-backup/terraform.tfstate.$(date +%Y%m%d)
```

### GKE Configuration Backup

GKE cluster configurations are backed up weekly using the Backup for GKE service:

1. **Create a Backup Plan**:
   ```bash
   gcloud beta container backup-restore backup-plans create gke-prod-backup \
     --project=matiks-prod-svc-a07 \
     --location=us-west1 \
     --cluster=gke-autopilot-prod-us-west1 \
     --all-namespaces
   ```

2. **Create a Backup**:
   ```bash
   gcloud beta container backup-restore backups create prod-backup-$(date +%Y%m%d) \
     --project=matiks-prod-svc-a07 \
     --location=us-west1 \
     --backup-plan=gke-prod-backup
   ```

## Testing and Validation

The disaster recovery procedures should be tested quarterly to ensure they work as expected:

1. Create a test environment that mimics production
2. Simulate various failure scenarios
3. Execute the recovery procedures
4. Document any issues or improvements
5. Update this document with lessons learned

## Contact Information

In case of emergency, contact:

- **Infrastructure Team**: <EMAIL>
- **On-Call Engineer**: +1-555-123-4567
- **Cloud Support**: Google Cloud Premium Support (support case ID: GCP-12345)

## Document Maintenance

This document should be reviewed and updated quarterly or whenever significant changes are made to the infrastructure.

Last updated: 2023-07-15