# Terraform Resource Inventory

This document provides an inventory of the current Terraform resources and their logical groupings to guide the modularization effort.

## Root Directory Resources

### Network Resources
- `network.tf`: Contains VPC, subnet, firewall, and other network-related resources
- `multi-region.tf`: Contains multi-region network configurations

### Project Resources
- `projects.tf`: Contains project definitions
- `service-projects.tf`: Contains service project configurations
- `folders.tf`: Contains folder structure definitions

### IAM Resources
- `iam.tf`: Contains IAM bindings and policies
- `service_accounts.tf`: Contains service account definitions
- `groups.tf`: Contains group definitions

### GKE Resources
- `gke.tf`: Contains GKE cluster configurations

### Logging and Monitoring Resources
- `log-export.tf`: Contains log export configurations
- `log-retention.tf`: Contains log retention policies
- `monitoring.tf`: Contains monitoring configurations

### Security Resources
- `org-policy.tf`: Contains organization policy configurations
- `security_scanning.tf`: Contains security scanning configurations

### Data Resources
- `data.tf`: Contains data sources
- `bigquery.tf`: Contains BigQuery resources

### Secret Management Resources
- `secret_manager.tf`: Contains Secret Manager configurations

### Infrastructure Configuration
- `backends.tf`: Contains backend configurations
- `providers.tf`: Contains provider configurations
- `variables.tf`: Contains variable definitions
- `outputs.tf`: Contains output definitions
- `versions.tf`: Contains version constraints
- `regions.tf`: Contains region definitions
- `module-versions.tf`: Contains module version constraints
- `testing.tf`: Contains testing configurations

## Projects Directory Resources

- `projects/data-science-projects.tf`: Contains data science project configurations
- `projects/host-projects.tf`: Contains host project configurations
- `projects/logging-monitoring-projects.tf`: Contains logging and monitoring project configurations
- `projects/matiks-service-projects.tf`: Contains service project configurations
- `projects/main.tf`: Contains main project configurations
- `projects/outputs.tf`: Contains project output definitions

## Existing Modules

- `modules/api-enablement`: Module for enabling GCP APIs
- `modules/gke_autopilot_cluster`: Module for GKE Autopilot cluster creation
- `modules/iam-role-assignment`: Module for IAM role assignments
- `modules/labels`: Module for resource labeling
- `modules/service-account`: Module for service account management

## Logical Groupings for Modularization

Based on the current resource organization, the following logical groupings are proposed for modularization:

1. **Network Module**
   - VPC submodule
   - Subnet submodule (with separate configurations for prod and non-prod)
   - Firewall submodule
   - NAT submodule
   - DNS submodule

2. **Project Module**
   - Project creation submodule
   - Folder structure submodule
   - Service project submodule
   - Host project submodule

3. **IAM Module**
   - Service account submodule
   - Role assignment submodule
   - Group management submodule
   - Policy binding submodule

4. **GKE Module**
   - Autopilot cluster submodule
   - Monitoring and logging submodule
   - Security submodule
   - Backup and recovery submodule

5. **Logging and Monitoring Module**
   - Log export submodule
   - Log sink submodule
   - Log retention submodule
   - Monitoring dashboard submodule
   - Alert policy submodule

6. **Security Module**
   - Organization policy submodule
   - Security scanning submodule
   - Compliance submodule

7. **Storage and Database Module**
   - Cloud Storage submodule
   - BigQuery submodule
   - Cloud SQL submodule

8. **Secret Management Module**
   - Secret Manager submodule
   - Key management submodule

9. **Dependency Management Module**
   - Resource dependency submodule
   - Module dependency submodule

10. **Disaster Recovery and Backup Module**
    - Backup configuration submodule
    - Recovery procedure submodule
