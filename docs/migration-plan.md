# Migration Plan

This document outlines the plan for migrating resources from the root-level Terraform files to the new modular structure.

## Overview

The current infrastructure is defined in root-level Terraform files, which need to be migrated to the new modular structure. This migration will be done in phases to minimize disruption and risk.

## Current State

The current infrastructure is defined in the following root-level Terraform files:

- `backends.tf`: Backend configuration
- `bigquery.tf`: BigQuery resources
- `data.tf`: Data sources
- `folders.tf`: Folder resources
- `gke.tf`: GKE resources
- `groups.tf`: Group resources
- `iam.tf`: IAM resources
- `log-export.tf`: Log export resources
- `log-retention.tf`: Log retention resources
- `module-versions.tf`: Module version constraints
- `monitoring.tf`: Monitoring resources
- `multi-region.tf`: Multi-region resources
- `network.tf`: Network resources
- `org-policy.tf`: Organization policy resources
- `outputs.tf`: Output values
- `projects.tf`: Project resources
- `providers.tf`: Provider configuration
- `regions.tf`: Region definitions
- `secret_manager.tf`: Secret Manager resources
- `security_scanning.tf`: Security scanning resources
- `service-projects.tf`: Service project resources
- `service_accounts.tf`: Service account resources
- `testing.tf`: Testing resources
- `variables.tf`: Input variables
- `versions.tf`: Terraform version constraints

## Target State

The target state is a modular structure with the following modules:

- `modules/network`: Network resources
- `modules/project`: Project resources
- `modules/iam`: IAM resources
- `modules/gke`: GKE resources
- `modules/storage`: Storage resources
- `modules/logging`: Logging resources
- `modules/monitoring`: Monitoring resources

And environment-specific configurations:

- `environments/prod`: Production environment
- `environments/nonprod`: Non-production environment
- `environments/dev`: Development environment

## Migration Strategy

The migration will be done in the following phases:

1. **Preparation**: Create a backup of the current state and set up the new directory structure
2. **State Migration**: Migrate the Terraform state to the new structure
3. **Resource Migration**: Migrate resources from root-level files to the new modules
4. **Testing**: Test the new structure to ensure it works as expected
5. **Cleanup**: Remove the old root-level files

## Detailed Migration Steps

### Phase 1: Preparation

1. Create a backup of the current state:
   ```bash
   terraform state pull > terraform.tfstate.backup
   ```

2. Set up the new directory structure (already done)

### Phase 2: State Migration

For each module, we'll need to migrate the state for the resources that will be managed by that module. This will be done using the `terraform state mv` command.

1. Use the provided script to migrate the state:
   ```bash
   ./scripts/migrate-state.sh
   ```

   This script will:
   - Create a backup of the current state
   - Migrate network resources
   - Migrate project resources
   - Migrate IAM resources
   - Migrate GKE resources
   - Migrate storage resources
   - Migrate logging resources
   - Migrate monitoring resources

2. Verify the state migration:
   ```bash
   terraform state list
   ```

### Phase 3: Resource Migration

For each root-level file, we'll need to migrate the resources to the appropriate module. This will be done by copying the resource definitions to the module files and updating the references.

1. Use the provided script to create environment configurations:
   ```bash
   ./scripts/migrate-resources.sh
   ```

   This script will:
   - Create environment directories (prod, nonprod, dev)
   - Create main.tf, variables.tf, outputs.tf, and versions.tf for each environment
   - Create terraform.tfvars.example for each environment
   - Create README.md for each environment

2. Review and update the generated files as needed:
   - Update the resource configurations to match your specific requirements
   - Update the variable values to match your environment
   - Update the outputs to include any additional information you need

3. Manually migrate any resources that were not automatically migrated:
   - Identify any resources that were not included in the script
   - Copy those resources to the appropriate module files
   - Update references to use the new module structure

### Phase 4: Testing

1. Initialize Terraform with the new structure:
   ```bash
   terraform init
   ```

2. Validate the new structure:
   ```bash
   terraform validate
   ```

3. Plan the changes:
   ```bash
   terraform plan
   ```

4. Apply the changes:
   ```bash
   terraform apply
   ```

5. Verify that the resources are created correctly

### Phase 5: Cleanup

1. Use the provided script to remove the old root-level files:
   ```bash
   ./scripts/cleanup-root-files.sh
   ```

   This script will:
   - List the root-level Terraform files that will be removed
   - Ask for confirmation before removing the files
   - Remove the files

2. Update the documentation to reflect the new structure

## Migration Risks and Mitigations

### Risks

1. **State Loss**: The Terraform state could be lost during migration
2. **Resource Recreation**: Resources could be recreated instead of being migrated
3. **Dependency Issues**: Dependencies between resources could be broken
4. **Downtime**: Services could experience downtime during migration

### Mitigations

1. **State Backup**: Create a backup of the state before migration
2. **Dry Run**: Test the migration in a non-production environment first
3. **Incremental Migration**: Migrate resources incrementally to minimize risk
4. **Rollback Plan**: Have a plan to rollback if issues occur
5. **Maintenance Window**: Schedule the migration during a maintenance window

## Post-Migration Tasks

1. **Documentation Update**: Update the documentation to reflect the new structure
2. **Knowledge Transfer**: Conduct knowledge transfer sessions for the team
3. **Monitoring**: Monitor the infrastructure for any issues
4. **Optimization**: Optimize the new structure based on feedback and performance

## Conclusion

This migration plan outlines the steps to migrate from the current root-level Terraform files to the new modular structure. By following this plan, we can minimize risk and ensure a successful migration.
