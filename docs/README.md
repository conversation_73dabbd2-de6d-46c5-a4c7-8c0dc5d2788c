# Google Cloud Platform Infrastructure Documentation

This directory contains comprehensive documentation for the Google Cloud Platform infrastructure managed by Terraform.

## Documentation Structure

- **[Modules](./modules/)**: Documentation for each Terraform module
- **[Examples](./examples/)**: Documentation for example configurations
- **[Guides](./guides/)**: Step-by-step guides for common tasks
- **[Tasks](./tasks.md)**: Task list for the modularization project
- **[Architecture](./architecture.md)**: Overall architecture documentation

## Getting Started

If you're new to this infrastructure, start with the following:

1. Read the [Architecture Overview](./architecture.md) to understand the overall design
2. Follow the [Quick Start Guide](./guides/quick-start.md) to set up your environment
3. Explore the [Module Documentation](./modules/) to understand the available modules
4. Review the [Examples](./examples/) to see how to use the modules

## Common Tasks

- [Creating a New Environment](./guides/new-environment.md)
- [Adding a New Project](./guides/new-project.md)
- [Deploying a GKE Cluster](./guides/gke-cluster.md)
- [Setting Up Monitoring and Logging](./guides/monitoring-logging.md)
- [Managing IAM Permissions](./guides/iam-management.md)

## Best Practices

- [Terraform Best Practices](./guides/terraform-best-practices.md)
- [Security Best Practices](./guides/security-best-practices.md)
- [Cost Optimization](./guides/cost-optimization.md)
- [Disaster Recovery](./guides/disaster-recovery.md)

## Contributing

- [Development Workflow](./guides/development-workflow.md)
- [Testing Guidelines](./guides/testing-guidelines.md)
- [Documentation Guidelines](./guides/documentation-guidelines.md)

## Support

For questions or issues, please contact the infrastructure team or create an issue in the repository.
