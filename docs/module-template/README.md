# [Module Name] Module

This module manages [brief description of what the module does].

## Features

- [Feature 1]
- [Feature 2]
- [Feature 3]

## Usage

```hcl
module "example" {
  source = "../../modules/[module-name]"

  # Required variables
  name        = "example-name"
  project_id  = "example-project"
  
  # Optional variables
  enable_feature = true
  custom_labels = {
    environment = "production"
    application = "example"
  }
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.3.0 |
| google | >= 4.80.0 |
| google-beta | >= 4.80.0 |

## Providers

| Name | Version |
|------|---------|
| google | >= 4.80.0 |
| google-beta | >= 4.80.0 |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| name | The name of the resource | `string` | n/a | yes |
| project_id | The ID of the project where resources will be created | `string` | n/a | yes |
| [other_variable] | [description] | `[type]` | `[default]` | [yes/no] |

## Outputs

| Name | Description |
|------|-------------|
| [output_name] | [description] |
| [another_output] | [description] |

## Submodules

This module has the following submodules:

- [Submodule 1]: [brief description]
- [Submodule 2]: [brief description]

## Examples

- [Basic Example](../../examples/[module-name]/basic)
- [Advanced Example](../../examples/[module-name]/advanced)

## Resources

This module creates the following resources:

- [Resource 1]
- [Resource 2]
- [Resource 3]

## Notes

- [Important note 1]
- [Important note 2]
