# Root outputs.tf - Exposes key outputs from the refactored modules.

# --- GKE Outputs --- (Moved from gke.tf)

output "gke_prod_cluster_name" {
  description = "Name of the production GKE cluster"
  value       = module.gke.cluster_names["prod"]
}

output "gke_prod_cluster_endpoint" {
  description = "Endpoint of the production GKE cluster"
  value       = module.gke.cluster_endpoints["prod"]
  sensitive   = true
}

output "gke_dev_cluster_name" {
  description = "Name of the development GKE cluster"
  value       = module.gke.cluster_names["dev"]
}

output "gke_dev_cluster_endpoint" {
  description = "Endpoint of the development GKE cluster"
  value       = module.gke.cluster_endpoints["dev"]
  sensitive   = true
}

# Add other root-level outputs here if needed, e.g., network IDs, project IDs.